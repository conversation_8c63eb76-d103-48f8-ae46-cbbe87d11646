// DO NOT EDIT.
// swift-format-ignore-file
// swiftlint:disable all
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: google/protobuf/wrappers.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// Wrappers for primitive (non-message) types. These types were needed
// for legacy reasons and are not recommended for use in new APIs.
//
// Historically these wrappers were useful to have presence on proto3 primitive
// fields, but proto3 syntax has been updated to support the `optional` keyword.
// Using that keyword is now the strongly preferred way to add presence to
// proto3 primitive fields.
//
// A secondary usecase was to embed primitives in the `google.protobuf.Any`
// type: it is now recommended that you embed your value in your own wrapper
// message which can be specifically documented.
//
// These wrappers have no meaningful use within repeated fields as they lack
// the ability to detect presence on individual elements.
// These wrappers have no meaningful use within a map or a oneof since
// individual entries of a map or fields of a oneof can already detect presence.

import Foundation
// 'import SwiftProtobuf' suppressed, this proto file is meant to be bundled in the runtime.

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: ProtobufAPIVersionCheck {
  struct _2: ProtobufAPIVersion_2 {}
  typealias Version = _2
}

/// Wrapper message for `double`.
///
/// The JSON representation for `DoubleValue` is JSON number.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_DoubleValue: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The double value.
  public var value: Double = 0

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `float`.
///
/// The JSON representation for `FloatValue` is JSON number.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_FloatValue: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The float value.
  public var value: Float = 0

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `int64`.
///
/// The JSON representation for `Int64Value` is JSON string.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_Int64Value: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The int64 value.
  public var value: Int64 = 0

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `uint64`.
///
/// The JSON representation for `UInt64Value` is JSON string.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_UInt64Value: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The uint64 value.
  public var value: UInt64 = 0

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `int32`.
///
/// The JSON representation for `Int32Value` is JSON number.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_Int32Value: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The int32 value.
  public var value: Int32 = 0

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `uint32`.
///
/// The JSON representation for `UInt32Value` is JSON number.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_UInt32Value: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The uint32 value.
  public var value: UInt32 = 0

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `bool`.
///
/// The JSON representation for `BoolValue` is JSON `true` and `false`.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_BoolValue: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The bool value.
  public var value: Bool = false

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `string`.
///
/// The JSON representation for `StringValue` is JSON string.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_StringValue: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The string value.
  public var value: String = String()

  public var unknownFields = UnknownStorage()

  public init() {}
}

/// Wrapper message for `bytes`.
///
/// The JSON representation for `BytesValue` is JSON string.
///
/// Not recommended for use in new APIs, but still useful for legacy APIs and
/// has no plan to be removed.
public struct Google_Protobuf_BytesValue: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The bytes value.
  public var value: Data = Data()

  public var unknownFields = UnknownStorage()

  public init() {}
}

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "google.protobuf"

extension Google_Protobuf_DoubleValue: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".DoubleValue"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularDoubleField(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value.bitPattern != 0 {
      try visitor.visitSingularDoubleField(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_DoubleValue, rhs: Google_Protobuf_DoubleValue) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FloatValue: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FloatValue"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularFloatField(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value.bitPattern != 0 {
      try visitor.visitSingularFloatField(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FloatValue, rhs: Google_Protobuf_FloatValue) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_Int64Value: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Int64Value"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt64Field(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value != 0 {
      try visitor.visitSingularInt64Field(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_Int64Value, rhs: Google_Protobuf_Int64Value) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_UInt64Value: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".UInt64Value"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt64Field(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value != 0 {
      try visitor.visitSingularUInt64Field(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_UInt64Value, rhs: Google_Protobuf_UInt64Value) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_Int32Value: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".Int32Value"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value != 0 {
      try visitor.visitSingularInt32Field(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_Int32Value, rhs: Google_Protobuf_Int32Value) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_UInt32Value: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".UInt32Value"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularUInt32Field(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value != 0 {
      try visitor.visitSingularUInt32Field(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_UInt32Value, rhs: Google_Protobuf_UInt32Value) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_BoolValue: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".BoolValue"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if self.value != false {
      try visitor.visitSingularBoolField(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_BoolValue, rhs: Google_Protobuf_BoolValue) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_StringValue: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".StringValue"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if !self.value.isEmpty {
      try visitor.visitSingularStringField(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_StringValue, rhs: Google_Protobuf_StringValue) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_BytesValue: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".BytesValue"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBytesField(value: &self.value) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if !self.value.isEmpty {
      try visitor.visitSingularBytesField(value: self.value, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_BytesValue, rhs: Google_Protobuf_BytesValue) -> Bool {
    if lhs.value != rhs.value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}
