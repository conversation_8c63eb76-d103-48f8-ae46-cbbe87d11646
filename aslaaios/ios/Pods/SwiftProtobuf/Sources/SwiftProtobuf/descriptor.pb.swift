// DO NOT EDIT.
// swift-format-ignore-file
// swiftlint:disable all
//
// Generated by the Swift generator plugin for the protocol buffer compiler.
// Source: google/protobuf/descriptor.proto
//
// For information on using the generated types, please see the documentation:
//   https://github.com/apple/swift-protobuf/

// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Kenton Varda)
//  Based on original Protocol Buffers design by
//  Sanjay Ghemawat, Jeff Dean, and others.
//
// The messages in this file describe the definitions found in .proto files.
// A valid .proto file can be translated directly to a FileDescriptorProto
// without any other information (e.g. without reading its imports).

import Foundation
// 'import SwiftProtobuf' suppressed, this proto file is meant to be bundled in the runtime.

// If the compiler emits an error on this type, it is because this file
// was generated by a version of the `protoc` Swift plug-in that is
// incompatible with the version of SwiftProtobuf to which you are linking.
// Please ensure that you are building against the same version of the API
// that was used to generate this file.
fileprivate struct _GeneratedWithProtocGenSwiftVersion: ProtobufAPIVersionCheck {
  struct _2: ProtobufAPIVersion_2 {}
  typealias Version = _2
}

/// The full set of known editions.
public enum Google_Protobuf_Edition: Int, Enum, Swift.CaseIterable {

  /// A placeholder for an unknown edition value.
  case unknown = 0

  /// A placeholder edition for specifying default behaviors *before* a feature
  /// was first introduced.  This is effectively an "infinite past".
  case legacy = 900

  /// Legacy syntax "editions".  These pre-date editions, but behave much like
  /// distinct editions.  These can't be used to specify the edition of proto
  /// files, but feature definitions must supply proto2/proto3 defaults for
  /// backwards compatibility.
  case proto2 = 998
  case proto3 = 999

  /// Editions that have been released.  The specific values are arbitrary and
  /// should not be depended on, but they will always be time-ordered for easy
  /// comparison.
  case edition2023 = 1000
  case edition2024 = 1001

  /// Placeholder editions for testing feature resolution.  These should not be
  /// used or relied on outside of tests.
  case edition1TestOnly = 1
  case edition2TestOnly = 2
  case edition99997TestOnly = 99997
  case edition99998TestOnly = 99998
  case edition99999TestOnly = 99999

  /// Placeholder for specifying unbounded edition support.  This should only
  /// ever be used by plugins that can expect to never require any changes to
  /// support a new edition.
  case max = 2147483647

  public init() {
    self = .unknown
  }

}

/// Describes the 'visibility' of a symbol with respect to the proto import
/// system. Symbols can only be imported when the visibility rules do not prevent
/// it (ex: local symbols cannot be imported).  Visibility modifiers can only set
/// on `message` and `enum` as they are the only types available to be referenced
/// from other files.
public enum Google_Protobuf_SymbolVisibility: Int, Enum, Swift.CaseIterable {
  case visibilityUnset = 0
  case visibilityLocal = 1
  case visibilityExport = 2

  public init() {
    self = .visibilityUnset
  }

}

/// The protocol compiler can output a FileDescriptorSet containing the .proto
/// files it parses.
public struct Google_Protobuf_FileDescriptorSet: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var file: [Google_Protobuf_FileDescriptorProto] = []

  public var unknownFields = UnknownStorage()

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
}

/// Describes a complete .proto file.
public struct Google_Protobuf_FileDescriptorProto: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// file name, relative to root of source tree
  public var name: String {
    get {return _name ?? String()}
    set {_name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return self._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {self._name = nil}

  /// e.g. "foo", "foo.bar", etc.
  public var package: String {
    get {return _package ?? String()}
    set {_package = newValue}
  }
  /// Returns true if `package` has been explicitly set.
  public var hasPackage: Bool {return self._package != nil}
  /// Clears the value of `package`. Subsequent reads from it will return its default value.
  public mutating func clearPackage() {self._package = nil}

  /// Names of files imported by this file.
  public var dependency: [String] = []

  /// Indexes of the public imported files in the dependency list above.
  public var publicDependency: [Int32] = []

  /// Indexes of the weak imported files in the dependency list.
  /// For Google-internal migration only. Do not use.
  public var weakDependency: [Int32] = []

  /// Names of files imported by this file purely for the purpose of providing
  /// option extensions. These are excluded from the dependency list above.
  public var optionDependency: [String] = []

  /// All top-level definitions in this file.
  public var messageType: [Google_Protobuf_DescriptorProto] = []

  public var enumType: [Google_Protobuf_EnumDescriptorProto] = []

  public var service: [Google_Protobuf_ServiceDescriptorProto] = []

  public var `extension`: [Google_Protobuf_FieldDescriptorProto] = []

  public var options: Google_Protobuf_FileOptions {
    get {return _options ?? Google_Protobuf_FileOptions()}
    set {_options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return self._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {self._options = nil}

  /// This field contains optional information about the original source code.
  /// You may safely remove this entire field without harming runtime
  /// functionality of the descriptors -- the information is needed only by
  /// development tools.
  public var sourceCodeInfo: Google_Protobuf_SourceCodeInfo {
    get {return _sourceCodeInfo ?? Google_Protobuf_SourceCodeInfo()}
    set {_sourceCodeInfo = newValue}
  }
  /// Returns true if `sourceCodeInfo` has been explicitly set.
  public var hasSourceCodeInfo: Bool {return self._sourceCodeInfo != nil}
  /// Clears the value of `sourceCodeInfo`. Subsequent reads from it will return its default value.
  public mutating func clearSourceCodeInfo() {self._sourceCodeInfo = nil}

  /// The syntax of the proto file.
  /// The supported values are "proto2", "proto3", and "editions".
  ///
  /// If `edition` is present, this value must be "editions".
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var syntax: String {
    get {return _syntax ?? String()}
    set {_syntax = newValue}
  }
  /// Returns true if `syntax` has been explicitly set.
  public var hasSyntax: Bool {return self._syntax != nil}
  /// Clears the value of `syntax`. Subsequent reads from it will return its default value.
  public mutating func clearSyntax() {self._syntax = nil}

  /// The edition of the proto file.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var edition: Google_Protobuf_Edition {
    get {return _edition ?? .unknown}
    set {_edition = newValue}
  }
  /// Returns true if `edition` has been explicitly set.
  public var hasEdition: Bool {return self._edition != nil}
  /// Clears the value of `edition`. Subsequent reads from it will return its default value.
  public mutating func clearEdition() {self._edition = nil}

  public var unknownFields = UnknownStorage()

  public init() {}

  fileprivate var _name: String? = nil
  fileprivate var _package: String? = nil
  fileprivate var _options: Google_Protobuf_FileOptions? = nil
  fileprivate var _sourceCodeInfo: Google_Protobuf_SourceCodeInfo? = nil
  fileprivate var _syntax: String? = nil
  fileprivate var _edition: Google_Protobuf_Edition? = nil
}

/// Describes a message type.
public struct Google_Protobuf_DescriptorProto: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _storage._name ?? String()}
    set {_uniqueStorage()._name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return _storage._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {_uniqueStorage()._name = nil}

  public var field: [Google_Protobuf_FieldDescriptorProto] {
    get {return _storage._field}
    set {_uniqueStorage()._field = newValue}
  }

  public var `extension`: [Google_Protobuf_FieldDescriptorProto] {
    get {return _storage._extension}
    set {_uniqueStorage()._extension = newValue}
  }

  public var nestedType: [Google_Protobuf_DescriptorProto] {
    get {return _storage._nestedType}
    set {_uniqueStorage()._nestedType = newValue}
  }

  public var enumType: [Google_Protobuf_EnumDescriptorProto] {
    get {return _storage._enumType}
    set {_uniqueStorage()._enumType = newValue}
  }

  public var extensionRange: [Google_Protobuf_DescriptorProto.ExtensionRange] {
    get {return _storage._extensionRange}
    set {_uniqueStorage()._extensionRange = newValue}
  }

  public var oneofDecl: [Google_Protobuf_OneofDescriptorProto] {
    get {return _storage._oneofDecl}
    set {_uniqueStorage()._oneofDecl = newValue}
  }

  public var options: Google_Protobuf_MessageOptions {
    get {return _storage._options ?? Google_Protobuf_MessageOptions()}
    set {_uniqueStorage()._options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return _storage._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {_uniqueStorage()._options = nil}

  public var reservedRange: [Google_Protobuf_DescriptorProto.ReservedRange] {
    get {return _storage._reservedRange}
    set {_uniqueStorage()._reservedRange = newValue}
  }

  /// Reserved field names, which may not be used by fields in the same message.
  /// A given name may only be reserved once.
  public var reservedName: [String] {
    get {return _storage._reservedName}
    set {_uniqueStorage()._reservedName = newValue}
  }

  /// Support for `export` and `local` keywords on enums.
  public var visibility: Google_Protobuf_SymbolVisibility {
    get {return _storage._visibility ?? .visibilityUnset}
    set {_uniqueStorage()._visibility = newValue}
  }
  /// Returns true if `visibility` has been explicitly set.
  public var hasVisibility: Bool {return _storage._visibility != nil}
  /// Clears the value of `visibility`. Subsequent reads from it will return its default value.
  public mutating func clearVisibility() {_uniqueStorage()._visibility = nil}

  public var unknownFields = UnknownStorage()

  public struct ExtensionRange: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// Inclusive.
    public var start: Int32 {
      get {return _start ?? 0}
      set {_start = newValue}
    }
    /// Returns true if `start` has been explicitly set.
    public var hasStart: Bool {return self._start != nil}
    /// Clears the value of `start`. Subsequent reads from it will return its default value.
    public mutating func clearStart() {self._start = nil}

    /// Exclusive.
    public var end: Int32 {
      get {return _end ?? 0}
      set {_end = newValue}
    }
    /// Returns true if `end` has been explicitly set.
    public var hasEnd: Bool {return self._end != nil}
    /// Clears the value of `end`. Subsequent reads from it will return its default value.
    public mutating func clearEnd() {self._end = nil}

    public var options: Google_Protobuf_ExtensionRangeOptions {
      get {return _options ?? Google_Protobuf_ExtensionRangeOptions()}
      set {_options = newValue}
    }
    /// Returns true if `options` has been explicitly set.
    public var hasOptions: Bool {return self._options != nil}
    /// Clears the value of `options`. Subsequent reads from it will return its default value.
    public mutating func clearOptions() {self._options = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _start: Int32? = nil
    fileprivate var _end: Int32? = nil
    fileprivate var _options: Google_Protobuf_ExtensionRangeOptions? = nil
  }

  /// Range of reserved tag numbers. Reserved tag numbers may not be used by
  /// fields or extension ranges in the same message. Reserved ranges may
  /// not overlap.
  public struct ReservedRange: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// Inclusive.
    public var start: Int32 {
      get {return _start ?? 0}
      set {_start = newValue}
    }
    /// Returns true if `start` has been explicitly set.
    public var hasStart: Bool {return self._start != nil}
    /// Clears the value of `start`. Subsequent reads from it will return its default value.
    public mutating func clearStart() {self._start = nil}

    /// Exclusive.
    public var end: Int32 {
      get {return _end ?? 0}
      set {_end = newValue}
    }
    /// Returns true if `end` has been explicitly set.
    public var hasEnd: Bool {return self._end != nil}
    /// Clears the value of `end`. Subsequent reads from it will return its default value.
    public mutating func clearEnd() {self._end = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _start: Int32? = nil
    fileprivate var _end: Int32? = nil
  }

  public init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

public struct Google_Protobuf_ExtensionRangeOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  /// For external users: DO NOT USE. We are in the process of open sourcing
  /// extension declaration and executing internal cleanups before it can be
  /// used externally.
  public var declaration: [Google_Protobuf_ExtensionRangeOptions.Declaration] = []

  /// Any features defined in the specific edition.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// The verification state of the range.
  /// TODO: flip the default to DECLARATION once all empty ranges
  /// are marked as UNVERIFIED.
  public var verification: Google_Protobuf_ExtensionRangeOptions.VerificationState {
    get {return _verification ?? .unverified}
    set {_verification = newValue}
  }
  /// Returns true if `verification` has been explicitly set.
  public var hasVerification: Bool {return self._verification != nil}
  /// Clears the value of `verification`. Subsequent reads from it will return its default value.
  public mutating func clearVerification() {self._verification = nil}

  public var unknownFields = UnknownStorage()

  /// The verification state of the extension range.
  public enum VerificationState: Int, Enum, Swift.CaseIterable {

    /// All the extensions of the range must be declared.
    case declaration = 0
    case unverified = 1

    public init() {
      self = .declaration
    }

  }

  public struct Declaration: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// The extension number declared within the extension range.
    public var number: Int32 {
      get {return _number ?? 0}
      set {_number = newValue}
    }
    /// Returns true if `number` has been explicitly set.
    public var hasNumber: Bool {return self._number != nil}
    /// Clears the value of `number`. Subsequent reads from it will return its default value.
    public mutating func clearNumber() {self._number = nil}

    /// The fully-qualified name of the extension field. There must be a leading
    /// dot in front of the full name.
    public var fullName: String {
      get {return _fullName ?? String()}
      set {_fullName = newValue}
    }
    /// Returns true if `fullName` has been explicitly set.
    public var hasFullName: Bool {return self._fullName != nil}
    /// Clears the value of `fullName`. Subsequent reads from it will return its default value.
    public mutating func clearFullName() {self._fullName = nil}

    /// The fully-qualified type name of the extension field. Unlike
    /// Metadata.type, Declaration.type must have a leading dot for messages
    /// and enums.
    public var type: String {
      get {return _type ?? String()}
      set {_type = newValue}
    }
    /// Returns true if `type` has been explicitly set.
    public var hasType: Bool {return self._type != nil}
    /// Clears the value of `type`. Subsequent reads from it will return its default value.
    public mutating func clearType() {self._type = nil}

    /// If true, indicates that the number is reserved in the extension range,
    /// and any extension field with the number will fail to compile. Set this
    /// when a declared extension field is deleted.
    public var reserved: Bool {
      get {return _reserved ?? false}
      set {_reserved = newValue}
    }
    /// Returns true if `reserved` has been explicitly set.
    public var hasReserved: Bool {return self._reserved != nil}
    /// Clears the value of `reserved`. Subsequent reads from it will return its default value.
    public mutating func clearReserved() {self._reserved = nil}

    /// If true, indicates that the extension must be defined as repeated.
    /// Otherwise the extension must be defined as optional.
    public var repeated: Bool {
      get {return _repeated ?? false}
      set {_repeated = newValue}
    }
    /// Returns true if `repeated` has been explicitly set.
    public var hasRepeated: Bool {return self._repeated != nil}
    /// Clears the value of `repeated`. Subsequent reads from it will return its default value.
    public mutating func clearRepeated() {self._repeated = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _number: Int32? = nil
    fileprivate var _fullName: String? = nil
    fileprivate var _type: String? = nil
    fileprivate var _reserved: Bool? = nil
    fileprivate var _repeated: Bool? = nil
  }

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
  fileprivate var _verification: Google_Protobuf_ExtensionRangeOptions.VerificationState? = nil
}

/// Describes a field within a message.
public struct Google_Protobuf_FieldDescriptorProto: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _name ?? String()}
    set {_name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return self._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {self._name = nil}

  public var number: Int32 {
    get {return _number ?? 0}
    set {_number = newValue}
  }
  /// Returns true if `number` has been explicitly set.
  public var hasNumber: Bool {return self._number != nil}
  /// Clears the value of `number`. Subsequent reads from it will return its default value.
  public mutating func clearNumber() {self._number = nil}

  public var label: Google_Protobuf_FieldDescriptorProto.Label {
    get {return _label ?? .optional}
    set {_label = newValue}
  }
  /// Returns true if `label` has been explicitly set.
  public var hasLabel: Bool {return self._label != nil}
  /// Clears the value of `label`. Subsequent reads from it will return its default value.
  public mutating func clearLabel() {self._label = nil}

  /// If type_name is set, this need not be set.  If both this and type_name
  /// are set, this must be one of TYPE_ENUM, TYPE_MESSAGE or TYPE_GROUP.
  public var type: Google_Protobuf_FieldDescriptorProto.TypeEnum {
    get {return _type ?? .double}
    set {_type = newValue}
  }
  /// Returns true if `type` has been explicitly set.
  public var hasType: Bool {return self._type != nil}
  /// Clears the value of `type`. Subsequent reads from it will return its default value.
  public mutating func clearType() {self._type = nil}

  /// For message and enum types, this is the name of the type.  If the name
  /// starts with a '.', it is fully-qualified.  Otherwise, C++-like scoping
  /// rules are used to find the type (i.e. first the nested types within this
  /// message are searched, then within the parent, on up to the root
  /// namespace).
  public var typeName: String {
    get {return _typeName ?? String()}
    set {_typeName = newValue}
  }
  /// Returns true if `typeName` has been explicitly set.
  public var hasTypeName: Bool {return self._typeName != nil}
  /// Clears the value of `typeName`. Subsequent reads from it will return its default value.
  public mutating func clearTypeName() {self._typeName = nil}

  /// For extensions, this is the name of the type being extended.  It is
  /// resolved in the same manner as type_name.
  public var extendee: String {
    get {return _extendee ?? String()}
    set {_extendee = newValue}
  }
  /// Returns true if `extendee` has been explicitly set.
  public var hasExtendee: Bool {return self._extendee != nil}
  /// Clears the value of `extendee`. Subsequent reads from it will return its default value.
  public mutating func clearExtendee() {self._extendee = nil}

  /// For numeric types, contains the original text representation of the value.
  /// For booleans, "true" or "false".
  /// For strings, contains the default text contents (not escaped in any way).
  /// For bytes, contains the C escaped value.  All bytes >= 128 are escaped.
  public var defaultValue: String {
    get {return _defaultValue ?? String()}
    set {_defaultValue = newValue}
  }
  /// Returns true if `defaultValue` has been explicitly set.
  public var hasDefaultValue: Bool {return self._defaultValue != nil}
  /// Clears the value of `defaultValue`. Subsequent reads from it will return its default value.
  public mutating func clearDefaultValue() {self._defaultValue = nil}

  /// If set, gives the index of a oneof in the containing type's oneof_decl
  /// list.  This field is a member of that oneof.
  public var oneofIndex: Int32 {
    get {return _oneofIndex ?? 0}
    set {_oneofIndex = newValue}
  }
  /// Returns true if `oneofIndex` has been explicitly set.
  public var hasOneofIndex: Bool {return self._oneofIndex != nil}
  /// Clears the value of `oneofIndex`. Subsequent reads from it will return its default value.
  public mutating func clearOneofIndex() {self._oneofIndex = nil}

  /// JSON name of this field. The value is set by protocol compiler. If the
  /// user has set a "json_name" option on this field, that option's value
  /// will be used. Otherwise, it's deduced from the field's name by converting
  /// it to camelCase.
  public var jsonName: String {
    get {return _jsonName ?? String()}
    set {_jsonName = newValue}
  }
  /// Returns true if `jsonName` has been explicitly set.
  public var hasJsonName: Bool {return self._jsonName != nil}
  /// Clears the value of `jsonName`. Subsequent reads from it will return its default value.
  public mutating func clearJsonName() {self._jsonName = nil}

  public var options: Google_Protobuf_FieldOptions {
    get {return _options ?? Google_Protobuf_FieldOptions()}
    set {_options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return self._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {self._options = nil}

  /// If true, this is a proto3 "optional". When a proto3 field is optional, it
  /// tracks presence regardless of field type.
  ///
  /// When proto3_optional is true, this field must belong to a oneof to signal
  /// to old proto3 clients that presence is tracked for this field. This oneof
  /// is known as a "synthetic" oneof, and this field must be its sole member
  /// (each proto3 optional field gets its own synthetic oneof). Synthetic oneofs
  /// exist in the descriptor only, and do not generate any API. Synthetic oneofs
  /// must be ordered after all "real" oneofs.
  ///
  /// For message fields, proto3_optional doesn't create any semantic change,
  /// since non-repeated message fields always track presence. However it still
  /// indicates the semantic detail of whether the user wrote "optional" or not.
  /// This can be useful for round-tripping the .proto file. For consistency we
  /// give message fields a synthetic oneof also, even though it is not required
  /// to track presence. This is especially important because the parser can't
  /// tell if a field is a message or an enum, so it must always create a
  /// synthetic oneof.
  ///
  /// Proto2 optional fields do not set this flag, because they already indicate
  /// optional with `LABEL_OPTIONAL`.
  public var proto3Optional: Bool {
    get {return _proto3Optional ?? false}
    set {_proto3Optional = newValue}
  }
  /// Returns true if `proto3Optional` has been explicitly set.
  public var hasProto3Optional: Bool {return self._proto3Optional != nil}
  /// Clears the value of `proto3Optional`. Subsequent reads from it will return its default value.
  public mutating func clearProto3Optional() {self._proto3Optional = nil}

  public var unknownFields = UnknownStorage()

  public enum TypeEnum: Int, Enum, Swift.CaseIterable {

    /// 0 is reserved for errors.
    /// Order is weird for historical reasons.
    case double = 1
    case float = 2

    /// Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if
    /// negative values are likely.
    case int64 = 3
    case uint64 = 4

    /// Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if
    /// negative values are likely.
    case int32 = 5
    case fixed64 = 6
    case fixed32 = 7
    case bool = 8
    case string = 9

    /// Tag-delimited aggregate.
    /// Group type is deprecated and not supported after google.protobuf. However, Proto3
    /// implementations should still be able to parse the group wire format and
    /// treat group fields as unknown fields.  In Editions, the group wire format
    /// can be enabled via the `message_encoding` feature.
    case group = 10

    /// Length-delimited aggregate.
    case message = 11

    /// New in version 2.
    case bytes = 12
    case uint32 = 13
    case `enum` = 14
    case sfixed32 = 15
    case sfixed64 = 16

    /// Uses ZigZag encoding.
    case sint32 = 17

    /// Uses ZigZag encoding.
    case sint64 = 18

    public init() {
      self = .double
    }

  }

  public enum Label: Int, Enum, Swift.CaseIterable {

    /// 0 is reserved for errors
    case `optional` = 1
    case repeated = 3

    /// The required label is only allowed in google.protobuf.  In proto3 and Editions
    /// it's explicitly prohibited.  In Editions, the `field_presence` feature
    /// can be used to get this behavior.
    case `required` = 2

    public init() {
      self = .optional
    }

  }

  public init() {}

  fileprivate var _name: String? = nil
  fileprivate var _number: Int32? = nil
  fileprivate var _label: Google_Protobuf_FieldDescriptorProto.Label? = nil
  fileprivate var _type: Google_Protobuf_FieldDescriptorProto.TypeEnum? = nil
  fileprivate var _typeName: String? = nil
  fileprivate var _extendee: String? = nil
  fileprivate var _defaultValue: String? = nil
  fileprivate var _oneofIndex: Int32? = nil
  fileprivate var _jsonName: String? = nil
  fileprivate var _options: Google_Protobuf_FieldOptions? = nil
  fileprivate var _proto3Optional: Bool? = nil
}

/// Describes a oneof.
public struct Google_Protobuf_OneofDescriptorProto: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _name ?? String()}
    set {_name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return self._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {self._name = nil}

  public var options: Google_Protobuf_OneofOptions {
    get {return _options ?? Google_Protobuf_OneofOptions()}
    set {_options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return self._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {self._options = nil}

  public var unknownFields = UnknownStorage()

  public init() {}

  fileprivate var _name: String? = nil
  fileprivate var _options: Google_Protobuf_OneofOptions? = nil
}

/// Describes an enum type.
public struct Google_Protobuf_EnumDescriptorProto: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _storage._name ?? String()}
    set {_uniqueStorage()._name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return _storage._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {_uniqueStorage()._name = nil}

  public var value: [Google_Protobuf_EnumValueDescriptorProto] {
    get {return _storage._value}
    set {_uniqueStorage()._value = newValue}
  }

  public var options: Google_Protobuf_EnumOptions {
    get {return _storage._options ?? Google_Protobuf_EnumOptions()}
    set {_uniqueStorage()._options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return _storage._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {_uniqueStorage()._options = nil}

  /// Range of reserved numeric values. Reserved numeric values may not be used
  /// by enum values in the same enum declaration. Reserved ranges may not
  /// overlap.
  public var reservedRange: [Google_Protobuf_EnumDescriptorProto.EnumReservedRange] {
    get {return _storage._reservedRange}
    set {_uniqueStorage()._reservedRange = newValue}
  }

  /// Reserved enum value names, which may not be reused. A given name may only
  /// be reserved once.
  public var reservedName: [String] {
    get {return _storage._reservedName}
    set {_uniqueStorage()._reservedName = newValue}
  }

  /// Support for `export` and `local` keywords on enums.
  public var visibility: Google_Protobuf_SymbolVisibility {
    get {return _storage._visibility ?? .visibilityUnset}
    set {_uniqueStorage()._visibility = newValue}
  }
  /// Returns true if `visibility` has been explicitly set.
  public var hasVisibility: Bool {return _storage._visibility != nil}
  /// Clears the value of `visibility`. Subsequent reads from it will return its default value.
  public mutating func clearVisibility() {_uniqueStorage()._visibility = nil}

  public var unknownFields = UnknownStorage()

  /// Range of reserved numeric values. Reserved values may not be used by
  /// entries in the same enum. Reserved ranges may not overlap.
  ///
  /// Note that this is distinct from DescriptorProto.ReservedRange in that it
  /// is inclusive such that it can appropriately represent the entire int32
  /// domain.
  public struct EnumReservedRange: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// Inclusive.
    public var start: Int32 {
      get {return _start ?? 0}
      set {_start = newValue}
    }
    /// Returns true if `start` has been explicitly set.
    public var hasStart: Bool {return self._start != nil}
    /// Clears the value of `start`. Subsequent reads from it will return its default value.
    public mutating func clearStart() {self._start = nil}

    /// Inclusive.
    public var end: Int32 {
      get {return _end ?? 0}
      set {_end = newValue}
    }
    /// Returns true if `end` has been explicitly set.
    public var hasEnd: Bool {return self._end != nil}
    /// Clears the value of `end`. Subsequent reads from it will return its default value.
    public mutating func clearEnd() {self._end = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _start: Int32? = nil
    fileprivate var _end: Int32? = nil
  }

  public init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

/// Describes a value within an enum.
public struct Google_Protobuf_EnumValueDescriptorProto: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _storage._name ?? String()}
    set {_uniqueStorage()._name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return _storage._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {_uniqueStorage()._name = nil}

  public var number: Int32 {
    get {return _storage._number ?? 0}
    set {_uniqueStorage()._number = newValue}
  }
  /// Returns true if `number` has been explicitly set.
  public var hasNumber: Bool {return _storage._number != nil}
  /// Clears the value of `number`. Subsequent reads from it will return its default value.
  public mutating func clearNumber() {_uniqueStorage()._number = nil}

  public var options: Google_Protobuf_EnumValueOptions {
    get {return _storage._options ?? Google_Protobuf_EnumValueOptions()}
    set {_uniqueStorage()._options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return _storage._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {_uniqueStorage()._options = nil}

  public var unknownFields = UnknownStorage()

  public init() {}

  fileprivate var _storage = _StorageClass.defaultInstance
}

/// Describes a service.
public struct Google_Protobuf_ServiceDescriptorProto: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _name ?? String()}
    set {_name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return self._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {self._name = nil}

  public var method: [Google_Protobuf_MethodDescriptorProto] = []

  public var options: Google_Protobuf_ServiceOptions {
    get {return _options ?? Google_Protobuf_ServiceOptions()}
    set {_options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return self._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {self._options = nil}

  public var unknownFields = UnknownStorage()

  public init() {}

  fileprivate var _name: String? = nil
  fileprivate var _options: Google_Protobuf_ServiceOptions? = nil
}

/// Describes a method of a service.
public struct Google_Protobuf_MethodDescriptorProto: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: String {
    get {return _name ?? String()}
    set {_name = newValue}
  }
  /// Returns true if `name` has been explicitly set.
  public var hasName: Bool {return self._name != nil}
  /// Clears the value of `name`. Subsequent reads from it will return its default value.
  public mutating func clearName() {self._name = nil}

  /// Input and output type names.  These are resolved in the same way as
  /// FieldDescriptorProto.type_name, but must refer to a message type.
  public var inputType: String {
    get {return _inputType ?? String()}
    set {_inputType = newValue}
  }
  /// Returns true if `inputType` has been explicitly set.
  public var hasInputType: Bool {return self._inputType != nil}
  /// Clears the value of `inputType`. Subsequent reads from it will return its default value.
  public mutating func clearInputType() {self._inputType = nil}

  public var outputType: String {
    get {return _outputType ?? String()}
    set {_outputType = newValue}
  }
  /// Returns true if `outputType` has been explicitly set.
  public var hasOutputType: Bool {return self._outputType != nil}
  /// Clears the value of `outputType`. Subsequent reads from it will return its default value.
  public mutating func clearOutputType() {self._outputType = nil}

  public var options: Google_Protobuf_MethodOptions {
    get {return _options ?? Google_Protobuf_MethodOptions()}
    set {_options = newValue}
  }
  /// Returns true if `options` has been explicitly set.
  public var hasOptions: Bool {return self._options != nil}
  /// Clears the value of `options`. Subsequent reads from it will return its default value.
  public mutating func clearOptions() {self._options = nil}

  /// Identifies if client streams multiple client messages
  public var clientStreaming: Bool {
    get {return _clientStreaming ?? false}
    set {_clientStreaming = newValue}
  }
  /// Returns true if `clientStreaming` has been explicitly set.
  public var hasClientStreaming: Bool {return self._clientStreaming != nil}
  /// Clears the value of `clientStreaming`. Subsequent reads from it will return its default value.
  public mutating func clearClientStreaming() {self._clientStreaming = nil}

  /// Identifies if server streams multiple server messages
  public var serverStreaming: Bool {
    get {return _serverStreaming ?? false}
    set {_serverStreaming = newValue}
  }
  /// Returns true if `serverStreaming` has been explicitly set.
  public var hasServerStreaming: Bool {return self._serverStreaming != nil}
  /// Clears the value of `serverStreaming`. Subsequent reads from it will return its default value.
  public mutating func clearServerStreaming() {self._serverStreaming = nil}

  public var unknownFields = UnknownStorage()

  public init() {}

  fileprivate var _name: String? = nil
  fileprivate var _inputType: String? = nil
  fileprivate var _outputType: String? = nil
  fileprivate var _options: Google_Protobuf_MethodOptions? = nil
  fileprivate var _clientStreaming: Bool? = nil
  fileprivate var _serverStreaming: Bool? = nil
}

public struct Google_Protobuf_FileOptions: ExtensibleMessage, @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Sets the Java package where classes generated from this .proto will be
  /// placed.  By default, the proto package is used, but this is often
  /// inappropriate because proto packages do not normally start with backwards
  /// domain names.
  public var javaPackage: String {
    get {return _storage._javaPackage ?? String()}
    set {_uniqueStorage()._javaPackage = newValue}
  }
  /// Returns true if `javaPackage` has been explicitly set.
  public var hasJavaPackage: Bool {return _storage._javaPackage != nil}
  /// Clears the value of `javaPackage`. Subsequent reads from it will return its default value.
  public mutating func clearJavaPackage() {_uniqueStorage()._javaPackage = nil}

  /// Controls the name of the wrapper Java class generated for the .proto file.
  /// That class will always contain the .proto file's getDescriptor() method as
  /// well as any top-level extensions defined in the .proto file.
  /// If java_multiple_files is disabled, then all the other classes from the
  /// .proto file will be nested inside the single wrapper outer class.
  public var javaOuterClassname: String {
    get {return _storage._javaOuterClassname ?? String()}
    set {_uniqueStorage()._javaOuterClassname = newValue}
  }
  /// Returns true if `javaOuterClassname` has been explicitly set.
  public var hasJavaOuterClassname: Bool {return _storage._javaOuterClassname != nil}
  /// Clears the value of `javaOuterClassname`. Subsequent reads from it will return its default value.
  public mutating func clearJavaOuterClassname() {_uniqueStorage()._javaOuterClassname = nil}

  /// If enabled, then the Java code generator will generate a separate .java
  /// file for each top-level message, enum, and service defined in the .proto
  /// file.  Thus, these types will *not* be nested inside the wrapper class
  /// named by java_outer_classname.  However, the wrapper class will still be
  /// generated to contain the file's getDescriptor() method as well as any
  /// top-level extensions defined in the file.
  public var javaMultipleFiles: Bool {
    get {return _storage._javaMultipleFiles ?? false}
    set {_uniqueStorage()._javaMultipleFiles = newValue}
  }
  /// Returns true if `javaMultipleFiles` has been explicitly set.
  public var hasJavaMultipleFiles: Bool {return _storage._javaMultipleFiles != nil}
  /// Clears the value of `javaMultipleFiles`. Subsequent reads from it will return its default value.
  public mutating func clearJavaMultipleFiles() {_uniqueStorage()._javaMultipleFiles = nil}

  /// This option does nothing.
  ///
  /// NOTE: This field was marked as deprecated in the .proto file.
  public var javaGenerateEqualsAndHash: Bool {
    get {return _storage._javaGenerateEqualsAndHash ?? false}
    set {_uniqueStorage()._javaGenerateEqualsAndHash = newValue}
  }
  /// Returns true if `javaGenerateEqualsAndHash` has been explicitly set.
  public var hasJavaGenerateEqualsAndHash: Bool {return _storage._javaGenerateEqualsAndHash != nil}
  /// Clears the value of `javaGenerateEqualsAndHash`. Subsequent reads from it will return its default value.
  public mutating func clearJavaGenerateEqualsAndHash() {_uniqueStorage()._javaGenerateEqualsAndHash = nil}

  /// A proto2 file can set this to true to opt in to UTF-8 checking for Java,
  /// which will throw an exception if invalid UTF-8 is parsed from the wire or
  /// assigned to a string field.
  ///
  /// TODO: clarify exactly what kinds of field types this option
  /// applies to, and update these docs accordingly.
  ///
  /// Proto3 files already perform these checks. Setting the option explicitly to
  /// false has no effect: it cannot be used to opt proto3 files out of UTF-8
  /// checks.
  public var javaStringCheckUtf8: Bool {
    get {return _storage._javaStringCheckUtf8 ?? false}
    set {_uniqueStorage()._javaStringCheckUtf8 = newValue}
  }
  /// Returns true if `javaStringCheckUtf8` has been explicitly set.
  public var hasJavaStringCheckUtf8: Bool {return _storage._javaStringCheckUtf8 != nil}
  /// Clears the value of `javaStringCheckUtf8`. Subsequent reads from it will return its default value.
  public mutating func clearJavaStringCheckUtf8() {_uniqueStorage()._javaStringCheckUtf8 = nil}

  public var optimizeFor: Google_Protobuf_FileOptions.OptimizeMode {
    get {return _storage._optimizeFor ?? .speed}
    set {_uniqueStorage()._optimizeFor = newValue}
  }
  /// Returns true if `optimizeFor` has been explicitly set.
  public var hasOptimizeFor: Bool {return _storage._optimizeFor != nil}
  /// Clears the value of `optimizeFor`. Subsequent reads from it will return its default value.
  public mutating func clearOptimizeFor() {_uniqueStorage()._optimizeFor = nil}

  /// Sets the Go package where structs generated from this .proto will be
  /// placed. If omitted, the Go package will be derived from the following:
  ///   - The basename of the package import path, if provided.
  ///   - Otherwise, the package statement in the .proto file, if present.
  ///   - Otherwise, the basename of the .proto file, without extension.
  public var goPackage: String {
    get {return _storage._goPackage ?? String()}
    set {_uniqueStorage()._goPackage = newValue}
  }
  /// Returns true if `goPackage` has been explicitly set.
  public var hasGoPackage: Bool {return _storage._goPackage != nil}
  /// Clears the value of `goPackage`. Subsequent reads from it will return its default value.
  public mutating func clearGoPackage() {_uniqueStorage()._goPackage = nil}

  /// Should generic services be generated in each language?  "Generic" services
  /// are not specific to any particular RPC system.  They are generated by the
  /// main code generators in each language (without additional plugins).
  /// Generic services were the only kind of service generation supported by
  /// early versions of google.protobuf.
  ///
  /// Generic services are now considered deprecated in favor of using plugins
  /// that generate code specific to your particular RPC system.  Therefore,
  /// these default to false.  Old code which depends on generic services should
  /// explicitly set them to true.
  public var ccGenericServices: Bool {
    get {return _storage._ccGenericServices ?? false}
    set {_uniqueStorage()._ccGenericServices = newValue}
  }
  /// Returns true if `ccGenericServices` has been explicitly set.
  public var hasCcGenericServices: Bool {return _storage._ccGenericServices != nil}
  /// Clears the value of `ccGenericServices`. Subsequent reads from it will return its default value.
  public mutating func clearCcGenericServices() {_uniqueStorage()._ccGenericServices = nil}

  public var javaGenericServices: Bool {
    get {return _storage._javaGenericServices ?? false}
    set {_uniqueStorage()._javaGenericServices = newValue}
  }
  /// Returns true if `javaGenericServices` has been explicitly set.
  public var hasJavaGenericServices: Bool {return _storage._javaGenericServices != nil}
  /// Clears the value of `javaGenericServices`. Subsequent reads from it will return its default value.
  public mutating func clearJavaGenericServices() {_uniqueStorage()._javaGenericServices = nil}

  public var pyGenericServices: Bool {
    get {return _storage._pyGenericServices ?? false}
    set {_uniqueStorage()._pyGenericServices = newValue}
  }
  /// Returns true if `pyGenericServices` has been explicitly set.
  public var hasPyGenericServices: Bool {return _storage._pyGenericServices != nil}
  /// Clears the value of `pyGenericServices`. Subsequent reads from it will return its default value.
  public mutating func clearPyGenericServices() {_uniqueStorage()._pyGenericServices = nil}

  /// Is this file deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for everything in the file, or it will be completely ignored; in the very
  /// least, this is a formalization for deprecating files.
  public var deprecated: Bool {
    get {return _storage._deprecated ?? false}
    set {_uniqueStorage()._deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return _storage._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {_uniqueStorage()._deprecated = nil}

  /// Enables the use of arenas for the proto messages in this file. This applies
  /// only to generated classes for C++.
  public var ccEnableArenas: Bool {
    get {return _storage._ccEnableArenas ?? true}
    set {_uniqueStorage()._ccEnableArenas = newValue}
  }
  /// Returns true if `ccEnableArenas` has been explicitly set.
  public var hasCcEnableArenas: Bool {return _storage._ccEnableArenas != nil}
  /// Clears the value of `ccEnableArenas`. Subsequent reads from it will return its default value.
  public mutating func clearCcEnableArenas() {_uniqueStorage()._ccEnableArenas = nil}

  /// Sets the objective c class prefix which is prepended to all objective c
  /// generated classes from this .proto. There is no default.
  public var objcClassPrefix: String {
    get {return _storage._objcClassPrefix ?? String()}
    set {_uniqueStorage()._objcClassPrefix = newValue}
  }
  /// Returns true if `objcClassPrefix` has been explicitly set.
  public var hasObjcClassPrefix: Bool {return _storage._objcClassPrefix != nil}
  /// Clears the value of `objcClassPrefix`. Subsequent reads from it will return its default value.
  public mutating func clearObjcClassPrefix() {_uniqueStorage()._objcClassPrefix = nil}

  /// Namespace for generated classes; defaults to the package.
  public var csharpNamespace: String {
    get {return _storage._csharpNamespace ?? String()}
    set {_uniqueStorage()._csharpNamespace = newValue}
  }
  /// Returns true if `csharpNamespace` has been explicitly set.
  public var hasCsharpNamespace: Bool {return _storage._csharpNamespace != nil}
  /// Clears the value of `csharpNamespace`. Subsequent reads from it will return its default value.
  public mutating func clearCsharpNamespace() {_uniqueStorage()._csharpNamespace = nil}

  /// By default Swift generators will take the proto package and CamelCase it
  /// replacing '.' with underscore and use that to prefix the types/symbols
  /// defined. When this options is provided, they will use this value instead
  /// to prefix the types/symbols defined.
  public var swiftPrefix: String {
    get {return _storage._swiftPrefix ?? String()}
    set {_uniqueStorage()._swiftPrefix = newValue}
  }
  /// Returns true if `swiftPrefix` has been explicitly set.
  public var hasSwiftPrefix: Bool {return _storage._swiftPrefix != nil}
  /// Clears the value of `swiftPrefix`. Subsequent reads from it will return its default value.
  public mutating func clearSwiftPrefix() {_uniqueStorage()._swiftPrefix = nil}

  /// Sets the php class prefix which is prepended to all php generated classes
  /// from this .proto. Default is empty.
  public var phpClassPrefix: String {
    get {return _storage._phpClassPrefix ?? String()}
    set {_uniqueStorage()._phpClassPrefix = newValue}
  }
  /// Returns true if `phpClassPrefix` has been explicitly set.
  public var hasPhpClassPrefix: Bool {return _storage._phpClassPrefix != nil}
  /// Clears the value of `phpClassPrefix`. Subsequent reads from it will return its default value.
  public mutating func clearPhpClassPrefix() {_uniqueStorage()._phpClassPrefix = nil}

  /// Use this option to change the namespace of php generated classes. Default
  /// is empty. When this option is empty, the package name will be used for
  /// determining the namespace.
  public var phpNamespace: String {
    get {return _storage._phpNamespace ?? String()}
    set {_uniqueStorage()._phpNamespace = newValue}
  }
  /// Returns true if `phpNamespace` has been explicitly set.
  public var hasPhpNamespace: Bool {return _storage._phpNamespace != nil}
  /// Clears the value of `phpNamespace`. Subsequent reads from it will return its default value.
  public mutating func clearPhpNamespace() {_uniqueStorage()._phpNamespace = nil}

  /// Use this option to change the namespace of php generated metadata classes.
  /// Default is empty. When this option is empty, the proto file name will be
  /// used for determining the namespace.
  public var phpMetadataNamespace: String {
    get {return _storage._phpMetadataNamespace ?? String()}
    set {_uniqueStorage()._phpMetadataNamespace = newValue}
  }
  /// Returns true if `phpMetadataNamespace` has been explicitly set.
  public var hasPhpMetadataNamespace: Bool {return _storage._phpMetadataNamespace != nil}
  /// Clears the value of `phpMetadataNamespace`. Subsequent reads from it will return its default value.
  public mutating func clearPhpMetadataNamespace() {_uniqueStorage()._phpMetadataNamespace = nil}

  /// Use this option to change the package of ruby generated classes. Default
  /// is empty. When this option is not set, the package name will be used for
  /// determining the ruby package.
  public var rubyPackage: String {
    get {return _storage._rubyPackage ?? String()}
    set {_uniqueStorage()._rubyPackage = newValue}
  }
  /// Returns true if `rubyPackage` has been explicitly set.
  public var hasRubyPackage: Bool {return _storage._rubyPackage != nil}
  /// Clears the value of `rubyPackage`. Subsequent reads from it will return its default value.
  public mutating func clearRubyPackage() {_uniqueStorage()._rubyPackage = nil}

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _storage._features ?? Google_Protobuf_FeatureSet()}
    set {_uniqueStorage()._features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return _storage._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {_uniqueStorage()._features = nil}

  /// The parser stores options it doesn't recognize here.
  /// See the documentation for the "Options" section above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] {
    get {return _storage._uninterpretedOption}
    set {_uniqueStorage()._uninterpretedOption = newValue}
  }

  public var unknownFields = UnknownStorage()

  /// Generated classes can be optimized for speed or code size.
  public enum OptimizeMode: Int, Enum, Swift.CaseIterable {

    /// Generate complete code for parsing, serialization,
    case speed = 1

    /// etc.
    case codeSize = 2

    /// Generate code using MessageLite and the lite runtime.
    case liteRuntime = 3

    public init() {
      self = .speed
    }

  }

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _storage = _StorageClass.defaultInstance
}

public struct Google_Protobuf_MessageOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Set true to use the old proto1 MessageSet wire format for extensions.
  /// This is provided for backwards-compatibility with the MessageSet wire
  /// format.  You should not use this for any other reason:  It's less
  /// efficient, has fewer features, and is more complicated.
  ///
  /// The message must be defined exactly as follows:
  ///   message Foo {
  ///     option message_set_wire_format = true;
  ///     extensions 4 to max;
  ///   }
  /// Note that the message cannot have any defined fields; MessageSets only
  /// have extensions.
  ///
  /// All extensions of your type must be singular messages; e.g. they cannot
  /// be int32s, enums, or repeated messages.
  ///
  /// Because this is an option, the above two restrictions are not enforced by
  /// the protocol compiler.
  public var messageSetWireFormat: Bool {
    get {return _messageSetWireFormat ?? false}
    set {_messageSetWireFormat = newValue}
  }
  /// Returns true if `messageSetWireFormat` has been explicitly set.
  public var hasMessageSetWireFormat: Bool {return self._messageSetWireFormat != nil}
  /// Clears the value of `messageSetWireFormat`. Subsequent reads from it will return its default value.
  public mutating func clearMessageSetWireFormat() {self._messageSetWireFormat = nil}

  /// Disables the generation of the standard "descriptor()" accessor, which can
  /// conflict with a field of the same name.  This is meant to make migration
  /// from proto1 easier; new code should avoid fields named "descriptor".
  public var noStandardDescriptorAccessor: Bool {
    get {return _noStandardDescriptorAccessor ?? false}
    set {_noStandardDescriptorAccessor = newValue}
  }
  /// Returns true if `noStandardDescriptorAccessor` has been explicitly set.
  public var hasNoStandardDescriptorAccessor: Bool {return self._noStandardDescriptorAccessor != nil}
  /// Clears the value of `noStandardDescriptorAccessor`. Subsequent reads from it will return its default value.
  public mutating func clearNoStandardDescriptorAccessor() {self._noStandardDescriptorAccessor = nil}

  /// Is this message deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for the message, or it will be completely ignored; in the very least,
  /// this is a formalization for deprecating messages.
  public var deprecated: Bool {
    get {return _deprecated ?? false}
    set {_deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return self._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {self._deprecated = nil}

  /// Whether the message is an automatically generated map entry type for the
  /// maps field.
  ///
  /// For maps fields:
  ///     map<KeyType, ValueType> map_field = 1;
  /// The parsed descriptor looks like:
  ///     message MapFieldEntry {
  ///         option map_entry = true;
  ///         optional KeyType key = 1;
  ///         optional ValueType value = 2;
  ///     }
  ///     repeated MapFieldEntry map_field = 1;
  ///
  /// Implementations may choose not to generate the map_entry=true message, but
  /// use a native map in the target language to hold the keys and values.
  /// The reflection APIs in such implementations still need to work as
  /// if the field is a repeated message field.
  ///
  /// NOTE: Do not set the option in .proto files. Always use the maps syntax
  /// instead. The option should only be implicitly set by the proto compiler
  /// parser.
  public var mapEntry: Bool {
    get {return _mapEntry ?? false}
    set {_mapEntry = newValue}
  }
  /// Returns true if `mapEntry` has been explicitly set.
  public var hasMapEntry: Bool {return self._mapEntry != nil}
  /// Clears the value of `mapEntry`. Subsequent reads from it will return its default value.
  public mutating func clearMapEntry() {self._mapEntry = nil}

  /// Enable the legacy handling of JSON field name conflicts.  This lowercases
  /// and strips underscored from the fields before comparison in proto3 only.
  /// The new behavior takes `json_name` into account and applies to proto2 as
  /// well.
  ///
  /// This should only be used as a temporary measure against broken builds due
  /// to the change in behavior for JSON field name conflicts.
  ///
  /// TODO This is legacy behavior we plan to remove once downstream
  /// teams have had time to migrate.
  ///
  /// NOTE: This field was marked as deprecated in the .proto file.
  public var deprecatedLegacyJsonFieldConflicts: Bool {
    get {return _deprecatedLegacyJsonFieldConflicts ?? false}
    set {_deprecatedLegacyJsonFieldConflicts = newValue}
  }
  /// Returns true if `deprecatedLegacyJsonFieldConflicts` has been explicitly set.
  public var hasDeprecatedLegacyJsonFieldConflicts: Bool {return self._deprecatedLegacyJsonFieldConflicts != nil}
  /// Clears the value of `deprecatedLegacyJsonFieldConflicts`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecatedLegacyJsonFieldConflicts() {self._deprecatedLegacyJsonFieldConflicts = nil}

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  public var unknownFields = UnknownStorage()

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _messageSetWireFormat: Bool? = nil
  fileprivate var _noStandardDescriptorAccessor: Bool? = nil
  fileprivate var _deprecated: Bool? = nil
  fileprivate var _mapEntry: Bool? = nil
  fileprivate var _deprecatedLegacyJsonFieldConflicts: Bool? = nil
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
}

public struct Google_Protobuf_FieldOptions: ExtensibleMessage, @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// NOTE: ctype is deprecated. Use `features.(pb.cpp).string_type` instead.
  /// The ctype option instructs the C++ code generator to use a different
  /// representation of the field than it normally would.  See the specific
  /// options below.  This option is only implemented to support use of
  /// [ctype=CORD] and [ctype=STRING] (the default) on non-repeated fields of
  /// type "bytes" in the open source release.
  /// TODO: make ctype actually deprecated.
  public var ctype: Google_Protobuf_FieldOptions.CType {
    get {return _storage._ctype ?? .string}
    set {_uniqueStorage()._ctype = newValue}
  }
  /// Returns true if `ctype` has been explicitly set.
  public var hasCtype: Bool {return _storage._ctype != nil}
  /// Clears the value of `ctype`. Subsequent reads from it will return its default value.
  public mutating func clearCtype() {_uniqueStorage()._ctype = nil}

  /// The packed option can be enabled for repeated primitive fields to enable
  /// a more efficient representation on the wire. Rather than repeatedly
  /// writing the tag and type for each element, the entire array is encoded as
  /// a single length-delimited blob. In proto3, only explicit setting it to
  /// false will avoid using packed encoding.  This option is prohibited in
  /// Editions, but the `repeated_field_encoding` feature can be used to control
  /// the behavior.
  public var packed: Bool {
    get {return _storage._packed ?? false}
    set {_uniqueStorage()._packed = newValue}
  }
  /// Returns true if `packed` has been explicitly set.
  public var hasPacked: Bool {return _storage._packed != nil}
  /// Clears the value of `packed`. Subsequent reads from it will return its default value.
  public mutating func clearPacked() {_uniqueStorage()._packed = nil}

  /// The jstype option determines the JavaScript type used for values of the
  /// field.  The option is permitted only for 64 bit integral and fixed types
  /// (int64, uint64, sint64, fixed64, sfixed64).  A field with jstype JS_STRING
  /// is represented as JavaScript string, which avoids loss of precision that
  /// can happen when a large value is converted to a floating point JavaScript.
  /// Specifying JS_NUMBER for the jstype causes the generated JavaScript code to
  /// use the JavaScript "number" type.  The behavior of the default option
  /// JS_NORMAL is implementation dependent.
  ///
  /// This option is an enum to permit additional types to be added, e.g.
  /// goog.math.Integer.
  public var jstype: Google_Protobuf_FieldOptions.JSType {
    get {return _storage._jstype ?? .jsNormal}
    set {_uniqueStorage()._jstype = newValue}
  }
  /// Returns true if `jstype` has been explicitly set.
  public var hasJstype: Bool {return _storage._jstype != nil}
  /// Clears the value of `jstype`. Subsequent reads from it will return its default value.
  public mutating func clearJstype() {_uniqueStorage()._jstype = nil}

  /// Should this field be parsed lazily?  Lazy applies only to message-type
  /// fields.  It means that when the outer message is initially parsed, the
  /// inner message's contents will not be parsed but instead stored in encoded
  /// form.  The inner message will actually be parsed when it is first accessed.
  ///
  /// This is only a hint.  Implementations are free to choose whether to use
  /// eager or lazy parsing regardless of the value of this option.  However,
  /// setting this option true suggests that the protocol author believes that
  /// using lazy parsing on this field is worth the additional bookkeeping
  /// overhead typically needed to implement it.
  ///
  /// This option does not affect the public interface of any generated code;
  /// all method signatures remain the same.  Furthermore, thread-safety of the
  /// interface is not affected by this option; const methods remain safe to
  /// call from multiple threads concurrently, while non-const methods continue
  /// to require exclusive access.
  ///
  /// Note that lazy message fields are still eagerly verified to check
  /// ill-formed wireformat or missing required fields. Calling IsInitialized()
  /// on the outer message would fail if the inner message has missing required
  /// fields. Failed verification would result in parsing failure (except when
  /// uninitialized messages are acceptable).
  public var lazy: Bool {
    get {return _storage._lazy ?? false}
    set {_uniqueStorage()._lazy = newValue}
  }
  /// Returns true if `lazy` has been explicitly set.
  public var hasLazy: Bool {return _storage._lazy != nil}
  /// Clears the value of `lazy`. Subsequent reads from it will return its default value.
  public mutating func clearLazy() {_uniqueStorage()._lazy = nil}

  /// unverified_lazy does no correctness checks on the byte stream. This should
  /// only be used where lazy with verification is prohibitive for performance
  /// reasons.
  public var unverifiedLazy: Bool {
    get {return _storage._unverifiedLazy ?? false}
    set {_uniqueStorage()._unverifiedLazy = newValue}
  }
  /// Returns true if `unverifiedLazy` has been explicitly set.
  public var hasUnverifiedLazy: Bool {return _storage._unverifiedLazy != nil}
  /// Clears the value of `unverifiedLazy`. Subsequent reads from it will return its default value.
  public mutating func clearUnverifiedLazy() {_uniqueStorage()._unverifiedLazy = nil}

  /// Is this field deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for accessors, or it will be completely ignored; in the very least, this
  /// is a formalization for deprecating fields.
  public var deprecated: Bool {
    get {return _storage._deprecated ?? false}
    set {_uniqueStorage()._deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return _storage._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {_uniqueStorage()._deprecated = nil}

  /// For Google-internal migration only. Do not use.
  public var weak: Bool {
    get {return _storage._weak ?? false}
    set {_uniqueStorage()._weak = newValue}
  }
  /// Returns true if `weak` has been explicitly set.
  public var hasWeak: Bool {return _storage._weak != nil}
  /// Clears the value of `weak`. Subsequent reads from it will return its default value.
  public mutating func clearWeak() {_uniqueStorage()._weak = nil}

  /// Indicate that the field value should not be printed out when using debug
  /// formats, e.g. when the field contains sensitive credentials.
  public var debugRedact: Bool {
    get {return _storage._debugRedact ?? false}
    set {_uniqueStorage()._debugRedact = newValue}
  }
  /// Returns true if `debugRedact` has been explicitly set.
  public var hasDebugRedact: Bool {return _storage._debugRedact != nil}
  /// Clears the value of `debugRedact`. Subsequent reads from it will return its default value.
  public mutating func clearDebugRedact() {_uniqueStorage()._debugRedact = nil}

  public var retention: Google_Protobuf_FieldOptions.OptionRetention {
    get {return _storage._retention ?? .retentionUnknown}
    set {_uniqueStorage()._retention = newValue}
  }
  /// Returns true if `retention` has been explicitly set.
  public var hasRetention: Bool {return _storage._retention != nil}
  /// Clears the value of `retention`. Subsequent reads from it will return its default value.
  public mutating func clearRetention() {_uniqueStorage()._retention = nil}

  public var targets: [Google_Protobuf_FieldOptions.OptionTargetType] {
    get {return _storage._targets}
    set {_uniqueStorage()._targets = newValue}
  }

  public var editionDefaults: [Google_Protobuf_FieldOptions.EditionDefault] {
    get {return _storage._editionDefaults}
    set {_uniqueStorage()._editionDefaults = newValue}
  }

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _storage._features ?? Google_Protobuf_FeatureSet()}
    set {_uniqueStorage()._features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return _storage._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {_uniqueStorage()._features = nil}

  public var featureSupport: Google_Protobuf_FieldOptions.FeatureSupport {
    get {return _storage._featureSupport ?? Google_Protobuf_FieldOptions.FeatureSupport()}
    set {_uniqueStorage()._featureSupport = newValue}
  }
  /// Returns true if `featureSupport` has been explicitly set.
  public var hasFeatureSupport: Bool {return _storage._featureSupport != nil}
  /// Clears the value of `featureSupport`. Subsequent reads from it will return its default value.
  public mutating func clearFeatureSupport() {_uniqueStorage()._featureSupport = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] {
    get {return _storage._uninterpretedOption}
    set {_uniqueStorage()._uninterpretedOption = newValue}
  }

  public var unknownFields = UnknownStorage()

  public enum CType: Int, Enum, Swift.CaseIterable {

    /// Default mode.
    case string = 0

    /// The option [ctype=CORD] may be applied to a non-repeated field of type
    /// "bytes". It indicates that in C++, the data should be stored in a Cord
    /// instead of a string.  For very large strings, this may reduce memory
    /// fragmentation. It may also allow better performance when parsing from a
    /// Cord, or when parsing with aliasing enabled, as the parsed Cord may then
    /// alias the original buffer.
    case cord = 1
    case stringPiece = 2

    public init() {
      self = .string
    }

  }

  public enum JSType: Int, Enum, Swift.CaseIterable {

    /// Use the default type.
    case jsNormal = 0

    /// Use JavaScript strings.
    case jsString = 1

    /// Use JavaScript numbers.
    case jsNumber = 2

    public init() {
      self = .jsNormal
    }

  }

  /// If set to RETENTION_SOURCE, the option will be omitted from the binary.
  public enum OptionRetention: Int, Enum, Swift.CaseIterable {
    case retentionUnknown = 0
    case retentionRuntime = 1
    case retentionSource = 2

    public init() {
      self = .retentionUnknown
    }

  }

  /// This indicates the types of entities that the field may apply to when used
  /// as an option. If it is unset, then the field may be freely used as an
  /// option on any kind of entity.
  public enum OptionTargetType: Int, Enum, Swift.CaseIterable {
    case targetTypeUnknown = 0
    case targetTypeFile = 1
    case targetTypeExtensionRange = 2
    case targetTypeMessage = 3
    case targetTypeField = 4
    case targetTypeOneof = 5
    case targetTypeEnum = 6
    case targetTypeEnumEntry = 7
    case targetTypeService = 8
    case targetTypeMethod = 9

    public init() {
      self = .targetTypeUnknown
    }

  }

  public struct EditionDefault: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    public var edition: Google_Protobuf_Edition {
      get {return _edition ?? .unknown}
      set {_edition = newValue}
    }
    /// Returns true if `edition` has been explicitly set.
    public var hasEdition: Bool {return self._edition != nil}
    /// Clears the value of `edition`. Subsequent reads from it will return its default value.
    public mutating func clearEdition() {self._edition = nil}

    /// Textproto value.
    public var value: String {
      get {return _value ?? String()}
      set {_value = newValue}
    }
    /// Returns true if `value` has been explicitly set.
    public var hasValue: Bool {return self._value != nil}
    /// Clears the value of `value`. Subsequent reads from it will return its default value.
    public mutating func clearValue() {self._value = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _edition: Google_Protobuf_Edition? = nil
    fileprivate var _value: String? = nil
  }

  /// Information about the support window of a feature.
  public struct FeatureSupport: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// The edition that this feature was first available in.  In editions
    /// earlier than this one, the default assigned to EDITION_LEGACY will be
    /// used, and proto files will not be able to override it.
    public var editionIntroduced: Google_Protobuf_Edition {
      get {return _editionIntroduced ?? .unknown}
      set {_editionIntroduced = newValue}
    }
    /// Returns true if `editionIntroduced` has been explicitly set.
    public var hasEditionIntroduced: Bool {return self._editionIntroduced != nil}
    /// Clears the value of `editionIntroduced`. Subsequent reads from it will return its default value.
    public mutating func clearEditionIntroduced() {self._editionIntroduced = nil}

    /// The edition this feature becomes deprecated in.  Using this after this
    /// edition may trigger warnings.
    public var editionDeprecated: Google_Protobuf_Edition {
      get {return _editionDeprecated ?? .unknown}
      set {_editionDeprecated = newValue}
    }
    /// Returns true if `editionDeprecated` has been explicitly set.
    public var hasEditionDeprecated: Bool {return self._editionDeprecated != nil}
    /// Clears the value of `editionDeprecated`. Subsequent reads from it will return its default value.
    public mutating func clearEditionDeprecated() {self._editionDeprecated = nil}

    /// The deprecation warning text if this feature is used after the edition it
    /// was marked deprecated in.
    public var deprecationWarning: String {
      get {return _deprecationWarning ?? String()}
      set {_deprecationWarning = newValue}
    }
    /// Returns true if `deprecationWarning` has been explicitly set.
    public var hasDeprecationWarning: Bool {return self._deprecationWarning != nil}
    /// Clears the value of `deprecationWarning`. Subsequent reads from it will return its default value.
    public mutating func clearDeprecationWarning() {self._deprecationWarning = nil}

    /// The edition this feature is no longer available in.  In editions after
    /// this one, the last default assigned will be used, and proto files will
    /// not be able to override it.
    public var editionRemoved: Google_Protobuf_Edition {
      get {return _editionRemoved ?? .unknown}
      set {_editionRemoved = newValue}
    }
    /// Returns true if `editionRemoved` has been explicitly set.
    public var hasEditionRemoved: Bool {return self._editionRemoved != nil}
    /// Clears the value of `editionRemoved`. Subsequent reads from it will return its default value.
    public mutating func clearEditionRemoved() {self._editionRemoved = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _editionIntroduced: Google_Protobuf_Edition? = nil
    fileprivate var _editionDeprecated: Google_Protobuf_Edition? = nil
    fileprivate var _deprecationWarning: String? = nil
    fileprivate var _editionRemoved: Google_Protobuf_Edition? = nil
  }

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _storage = _StorageClass.defaultInstance
}

public struct Google_Protobuf_OneofOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  public var unknownFields = UnknownStorage()

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
}

public struct Google_Protobuf_EnumOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Set this option to true to allow mapping different tag names to the same
  /// value.
  public var allowAlias: Bool {
    get {return _allowAlias ?? false}
    set {_allowAlias = newValue}
  }
  /// Returns true if `allowAlias` has been explicitly set.
  public var hasAllowAlias: Bool {return self._allowAlias != nil}
  /// Clears the value of `allowAlias`. Subsequent reads from it will return its default value.
  public mutating func clearAllowAlias() {self._allowAlias = nil}

  /// Is this enum deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for the enum, or it will be completely ignored; in the very least, this
  /// is a formalization for deprecating enums.
  public var deprecated: Bool {
    get {return _deprecated ?? false}
    set {_deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return self._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {self._deprecated = nil}

  /// Enable the legacy handling of JSON field name conflicts.  This lowercases
  /// and strips underscored from the fields before comparison in proto3 only.
  /// The new behavior takes `json_name` into account and applies to proto2 as
  /// well.
  /// TODO Remove this legacy behavior once downstream teams have
  /// had time to migrate.
  ///
  /// NOTE: This field was marked as deprecated in the .proto file.
  public var deprecatedLegacyJsonFieldConflicts: Bool {
    get {return _deprecatedLegacyJsonFieldConflicts ?? false}
    set {_deprecatedLegacyJsonFieldConflicts = newValue}
  }
  /// Returns true if `deprecatedLegacyJsonFieldConflicts` has been explicitly set.
  public var hasDeprecatedLegacyJsonFieldConflicts: Bool {return self._deprecatedLegacyJsonFieldConflicts != nil}
  /// Clears the value of `deprecatedLegacyJsonFieldConflicts`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecatedLegacyJsonFieldConflicts() {self._deprecatedLegacyJsonFieldConflicts = nil}

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  public var unknownFields = UnknownStorage()

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _allowAlias: Bool? = nil
  fileprivate var _deprecated: Bool? = nil
  fileprivate var _deprecatedLegacyJsonFieldConflicts: Bool? = nil
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
}

public struct Google_Protobuf_EnumValueOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Is this enum value deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for the enum value, or it will be completely ignored; in the very least,
  /// this is a formalization for deprecating enum values.
  public var deprecated: Bool {
    get {return _deprecated ?? false}
    set {_deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return self._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {self._deprecated = nil}

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// Indicate that fields annotated with this enum value should not be printed
  /// out when using debug formats, e.g. when the field contains sensitive
  /// credentials.
  public var debugRedact: Bool {
    get {return _debugRedact ?? false}
    set {_debugRedact = newValue}
  }
  /// Returns true if `debugRedact` has been explicitly set.
  public var hasDebugRedact: Bool {return self._debugRedact != nil}
  /// Clears the value of `debugRedact`. Subsequent reads from it will return its default value.
  public mutating func clearDebugRedact() {self._debugRedact = nil}

  /// Information about the support window of a feature value.
  public var featureSupport: Google_Protobuf_FieldOptions.FeatureSupport {
    get {return _featureSupport ?? Google_Protobuf_FieldOptions.FeatureSupport()}
    set {_featureSupport = newValue}
  }
  /// Returns true if `featureSupport` has been explicitly set.
  public var hasFeatureSupport: Bool {return self._featureSupport != nil}
  /// Clears the value of `featureSupport`. Subsequent reads from it will return its default value.
  public mutating func clearFeatureSupport() {self._featureSupport = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  public var unknownFields = UnknownStorage()

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _deprecated: Bool? = nil
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
  fileprivate var _debugRedact: Bool? = nil
  fileprivate var _featureSupport: Google_Protobuf_FieldOptions.FeatureSupport? = nil
}

public struct Google_Protobuf_ServiceOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// Is this service deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for the service, or it will be completely ignored; in the very least,
  /// this is a formalization for deprecating services.
  public var deprecated: Bool {
    get {return _deprecated ?? false}
    set {_deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return self._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {self._deprecated = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  public var unknownFields = UnknownStorage()

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
  fileprivate var _deprecated: Bool? = nil
}

public struct Google_Protobuf_MethodOptions: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// Is this method deprecated?
  /// Depending on the target platform, this can emit Deprecated annotations
  /// for the method, or it will be completely ignored; in the very least,
  /// this is a formalization for deprecating methods.
  public var deprecated: Bool {
    get {return _deprecated ?? false}
    set {_deprecated = newValue}
  }
  /// Returns true if `deprecated` has been explicitly set.
  public var hasDeprecated: Bool {return self._deprecated != nil}
  /// Clears the value of `deprecated`. Subsequent reads from it will return its default value.
  public mutating func clearDeprecated() {self._deprecated = nil}

  public var idempotencyLevel: Google_Protobuf_MethodOptions.IdempotencyLevel {
    get {return _idempotencyLevel ?? .idempotencyUnknown}
    set {_idempotencyLevel = newValue}
  }
  /// Returns true if `idempotencyLevel` has been explicitly set.
  public var hasIdempotencyLevel: Bool {return self._idempotencyLevel != nil}
  /// Clears the value of `idempotencyLevel`. Subsequent reads from it will return its default value.
  public mutating func clearIdempotencyLevel() {self._idempotencyLevel = nil}

  /// Any features defined in the specific edition.
  /// WARNING: This field should only be used by protobuf plugins or special
  /// cases like the proto compiler. Other uses are discouraged and
  /// developers should rely on the protoreflect APIs for their client language.
  public var features: Google_Protobuf_FeatureSet {
    get {return _features ?? Google_Protobuf_FeatureSet()}
    set {_features = newValue}
  }
  /// Returns true if `features` has been explicitly set.
  public var hasFeatures: Bool {return self._features != nil}
  /// Clears the value of `features`. Subsequent reads from it will return its default value.
  public mutating func clearFeatures() {self._features = nil}

  /// The parser stores options it doesn't recognize here. See above.
  public var uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

  public var unknownFields = UnknownStorage()

  /// Is this method side-effect-free (or safe in HTTP parlance), or idempotent,
  /// or neither? HTTP based RPC implementation may choose GET verb for safe
  /// methods, and PUT verb for idempotent methods instead of the default POST.
  public enum IdempotencyLevel: Int, Enum, Swift.CaseIterable {
    case idempotencyUnknown = 0

    /// implies idempotent
    case noSideEffects = 1

    /// idempotent, but may have side effects
    case idempotent = 2

    public init() {
      self = .idempotencyUnknown
    }

  }

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _deprecated: Bool? = nil
  fileprivate var _idempotencyLevel: Google_Protobuf_MethodOptions.IdempotencyLevel? = nil
  fileprivate var _features: Google_Protobuf_FeatureSet? = nil
}

/// A message representing a option the parser does not recognize. This only
/// appears in options protos created by the compiler::Parser class.
/// DescriptorPool resolves these when building Descriptor objects. Therefore,
/// options protos in descriptor objects (e.g. returned by Descriptor::options(),
/// or produced by Descriptor::CopyTo()) will never have UninterpretedOptions
/// in them.
public struct Google_Protobuf_UninterpretedOption: @unchecked Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var name: [Google_Protobuf_UninterpretedOption.NamePart] = []

  /// The value of the uninterpreted option, in whatever type the tokenizer
  /// identified it as during parsing. Exactly one of these should be set.
  public var identifierValue: String {
    get {return _identifierValue ?? String()}
    set {_identifierValue = newValue}
  }
  /// Returns true if `identifierValue` has been explicitly set.
  public var hasIdentifierValue: Bool {return self._identifierValue != nil}
  /// Clears the value of `identifierValue`. Subsequent reads from it will return its default value.
  public mutating func clearIdentifierValue() {self._identifierValue = nil}

  public var positiveIntValue: UInt64 {
    get {return _positiveIntValue ?? 0}
    set {_positiveIntValue = newValue}
  }
  /// Returns true if `positiveIntValue` has been explicitly set.
  public var hasPositiveIntValue: Bool {return self._positiveIntValue != nil}
  /// Clears the value of `positiveIntValue`. Subsequent reads from it will return its default value.
  public mutating func clearPositiveIntValue() {self._positiveIntValue = nil}

  public var negativeIntValue: Int64 {
    get {return _negativeIntValue ?? 0}
    set {_negativeIntValue = newValue}
  }
  /// Returns true if `negativeIntValue` has been explicitly set.
  public var hasNegativeIntValue: Bool {return self._negativeIntValue != nil}
  /// Clears the value of `negativeIntValue`. Subsequent reads from it will return its default value.
  public mutating func clearNegativeIntValue() {self._negativeIntValue = nil}

  public var doubleValue: Double {
    get {return _doubleValue ?? 0}
    set {_doubleValue = newValue}
  }
  /// Returns true if `doubleValue` has been explicitly set.
  public var hasDoubleValue: Bool {return self._doubleValue != nil}
  /// Clears the value of `doubleValue`. Subsequent reads from it will return its default value.
  public mutating func clearDoubleValue() {self._doubleValue = nil}

  public var stringValue: Data {
    get {return _stringValue ?? Data()}
    set {_stringValue = newValue}
  }
  /// Returns true if `stringValue` has been explicitly set.
  public var hasStringValue: Bool {return self._stringValue != nil}
  /// Clears the value of `stringValue`. Subsequent reads from it will return its default value.
  public mutating func clearStringValue() {self._stringValue = nil}

  public var aggregateValue: String {
    get {return _aggregateValue ?? String()}
    set {_aggregateValue = newValue}
  }
  /// Returns true if `aggregateValue` has been explicitly set.
  public var hasAggregateValue: Bool {return self._aggregateValue != nil}
  /// Clears the value of `aggregateValue`. Subsequent reads from it will return its default value.
  public mutating func clearAggregateValue() {self._aggregateValue = nil}

  public var unknownFields = UnknownStorage()

  /// The name of the uninterpreted option.  Each string represents a segment in
  /// a dot-separated name.  is_extension is true iff a segment represents an
  /// extension (denoted with parentheses in options specs in .proto files).
  /// E.g.,{ ["foo", false], ["bar.baz", true], ["moo", false] } represents
  /// "foo.(bar.baz).moo".
  public struct NamePart: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    public var namePart: String {
      get {return _namePart ?? String()}
      set {_namePart = newValue}
    }
    /// Returns true if `namePart` has been explicitly set.
    public var hasNamePart: Bool {return self._namePart != nil}
    /// Clears the value of `namePart`. Subsequent reads from it will return its default value.
    public mutating func clearNamePart() {self._namePart = nil}

    public var isExtension: Bool {
      get {return _isExtension ?? false}
      set {_isExtension = newValue}
    }
    /// Returns true if `isExtension` has been explicitly set.
    public var hasIsExtension: Bool {return self._isExtension != nil}
    /// Clears the value of `isExtension`. Subsequent reads from it will return its default value.
    public mutating func clearIsExtension() {self._isExtension = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _namePart: String? = nil
    fileprivate var _isExtension: Bool? = nil
  }

  public init() {}

  fileprivate var _identifierValue: String? = nil
  fileprivate var _positiveIntValue: UInt64? = nil
  fileprivate var _negativeIntValue: Int64? = nil
  fileprivate var _doubleValue: Double? = nil
  fileprivate var _stringValue: Data? = nil
  fileprivate var _aggregateValue: String? = nil
}

/// TODO Enums in C++ gencode (and potentially other languages) are
/// not well scoped.  This means that each of the feature enums below can clash
/// with each other.  The short names we've chosen maximize call-site
/// readability, but leave us very open to this scenario.  A future feature will
/// be designed and implemented to handle this, hopefully before we ever hit a
/// conflict here.
public struct Google_Protobuf_FeatureSet: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var fieldPresence: Google_Protobuf_FeatureSet.FieldPresence {
    get {return _fieldPresence ?? .unknown}
    set {_fieldPresence = newValue}
  }
  /// Returns true if `fieldPresence` has been explicitly set.
  public var hasFieldPresence: Bool {return self._fieldPresence != nil}
  /// Clears the value of `fieldPresence`. Subsequent reads from it will return its default value.
  public mutating func clearFieldPresence() {self._fieldPresence = nil}

  public var enumType: Google_Protobuf_FeatureSet.EnumType {
    get {return _enumType ?? .unknown}
    set {_enumType = newValue}
  }
  /// Returns true if `enumType` has been explicitly set.
  public var hasEnumType: Bool {return self._enumType != nil}
  /// Clears the value of `enumType`. Subsequent reads from it will return its default value.
  public mutating func clearEnumType() {self._enumType = nil}

  public var repeatedFieldEncoding: Google_Protobuf_FeatureSet.RepeatedFieldEncoding {
    get {return _repeatedFieldEncoding ?? .unknown}
    set {_repeatedFieldEncoding = newValue}
  }
  /// Returns true if `repeatedFieldEncoding` has been explicitly set.
  public var hasRepeatedFieldEncoding: Bool {return self._repeatedFieldEncoding != nil}
  /// Clears the value of `repeatedFieldEncoding`. Subsequent reads from it will return its default value.
  public mutating func clearRepeatedFieldEncoding() {self._repeatedFieldEncoding = nil}

  public var utf8Validation: Google_Protobuf_FeatureSet.Utf8Validation {
    get {return _utf8Validation ?? .unknown}
    set {_utf8Validation = newValue}
  }
  /// Returns true if `utf8Validation` has been explicitly set.
  public var hasUtf8Validation: Bool {return self._utf8Validation != nil}
  /// Clears the value of `utf8Validation`. Subsequent reads from it will return its default value.
  public mutating func clearUtf8Validation() {self._utf8Validation = nil}

  public var messageEncoding: Google_Protobuf_FeatureSet.MessageEncoding {
    get {return _messageEncoding ?? .unknown}
    set {_messageEncoding = newValue}
  }
  /// Returns true if `messageEncoding` has been explicitly set.
  public var hasMessageEncoding: Bool {return self._messageEncoding != nil}
  /// Clears the value of `messageEncoding`. Subsequent reads from it will return its default value.
  public mutating func clearMessageEncoding() {self._messageEncoding = nil}

  public var jsonFormat: Google_Protobuf_FeatureSet.JsonFormat {
    get {return _jsonFormat ?? .unknown}
    set {_jsonFormat = newValue}
  }
  /// Returns true if `jsonFormat` has been explicitly set.
  public var hasJsonFormat: Bool {return self._jsonFormat != nil}
  /// Clears the value of `jsonFormat`. Subsequent reads from it will return its default value.
  public mutating func clearJsonFormat() {self._jsonFormat = nil}

  public var enforceNamingStyle: Google_Protobuf_FeatureSet.EnforceNamingStyle {
    get {return _enforceNamingStyle ?? .unknown}
    set {_enforceNamingStyle = newValue}
  }
  /// Returns true if `enforceNamingStyle` has been explicitly set.
  public var hasEnforceNamingStyle: Bool {return self._enforceNamingStyle != nil}
  /// Clears the value of `enforceNamingStyle`. Subsequent reads from it will return its default value.
  public mutating func clearEnforceNamingStyle() {self._enforceNamingStyle = nil}

  public var defaultSymbolVisibility: Google_Protobuf_FeatureSet.VisibilityFeature.DefaultSymbolVisibility {
    get {return _defaultSymbolVisibility ?? .unknown}
    set {_defaultSymbolVisibility = newValue}
  }
  /// Returns true if `defaultSymbolVisibility` has been explicitly set.
  public var hasDefaultSymbolVisibility: Bool {return self._defaultSymbolVisibility != nil}
  /// Clears the value of `defaultSymbolVisibility`. Subsequent reads from it will return its default value.
  public mutating func clearDefaultSymbolVisibility() {self._defaultSymbolVisibility = nil}

  public var unknownFields = UnknownStorage()

  public enum FieldPresence: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case explicit = 1
    case implicit = 2
    case legacyRequired = 3

    public init() {
      self = .unknown
    }

  }

  public enum EnumType: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case `open` = 1
    case closed = 2

    public init() {
      self = .unknown
    }

  }

  public enum RepeatedFieldEncoding: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case packed = 1
    case expanded = 2

    public init() {
      self = .unknown
    }

  }

  public enum Utf8Validation: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case verify = 2
    case none = 3

    public init() {
      self = .unknown
    }

  }

  public enum MessageEncoding: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case lengthPrefixed = 1
    case delimited = 2

    public init() {
      self = .unknown
    }

  }

  public enum JsonFormat: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case allow = 1
    case legacyBestEffort = 2

    public init() {
      self = .unknown
    }

  }

  public enum EnforceNamingStyle: Int, Enum, Swift.CaseIterable {
    case unknown = 0
    case style2024 = 1
    case styleLegacy = 2

    public init() {
      self = .unknown
    }

  }

  public struct VisibilityFeature: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    public var unknownFields = UnknownStorage()

    public enum DefaultSymbolVisibility: Int, Enum, Swift.CaseIterable {
      case unknown = 0

      /// Default pre-EDITION_2024, all UNSET visibility are export.
      case exportAll = 1

      /// All top-level symbols default to export, nested default to local.
      case exportTopLevel = 2

      /// All symbols default to local.
      case localAll = 3

      /// All symbols local by default. Nested types cannot be exported.
      /// With special case caveat for message { enum {} reserved 1 to max; }
      /// This is the recommended setting for new protos.
      case strict = 4

      public init() {
        self = .unknown
      }

    }

    public init() {}
  }

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
  fileprivate var _fieldPresence: Google_Protobuf_FeatureSet.FieldPresence? = nil
  fileprivate var _enumType: Google_Protobuf_FeatureSet.EnumType? = nil
  fileprivate var _repeatedFieldEncoding: Google_Protobuf_FeatureSet.RepeatedFieldEncoding? = nil
  fileprivate var _utf8Validation: Google_Protobuf_FeatureSet.Utf8Validation? = nil
  fileprivate var _messageEncoding: Google_Protobuf_FeatureSet.MessageEncoding? = nil
  fileprivate var _jsonFormat: Google_Protobuf_FeatureSet.JsonFormat? = nil
  fileprivate var _enforceNamingStyle: Google_Protobuf_FeatureSet.EnforceNamingStyle? = nil
  fileprivate var _defaultSymbolVisibility: Google_Protobuf_FeatureSet.VisibilityFeature.DefaultSymbolVisibility? = nil
}

/// A compiled specification for the defaults of a set of features.  These
/// messages are generated from FeatureSet extensions and can be used to seed
/// feature resolution. The resolution with this object becomes a simple search
/// for the closest matching edition, followed by proto merges.
public struct Google_Protobuf_FeatureSetDefaults: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  public var defaults: [Google_Protobuf_FeatureSetDefaults.FeatureSetEditionDefault] = []

  /// The minimum supported edition (inclusive) when this was constructed.
  /// Editions before this will not have defaults.
  public var minimumEdition: Google_Protobuf_Edition {
    get {return _minimumEdition ?? .unknown}
    set {_minimumEdition = newValue}
  }
  /// Returns true if `minimumEdition` has been explicitly set.
  public var hasMinimumEdition: Bool {return self._minimumEdition != nil}
  /// Clears the value of `minimumEdition`. Subsequent reads from it will return its default value.
  public mutating func clearMinimumEdition() {self._minimumEdition = nil}

  /// The maximum known edition (inclusive) when this was constructed. Editions
  /// after this will not have reliable defaults.
  public var maximumEdition: Google_Protobuf_Edition {
    get {return _maximumEdition ?? .unknown}
    set {_maximumEdition = newValue}
  }
  /// Returns true if `maximumEdition` has been explicitly set.
  public var hasMaximumEdition: Bool {return self._maximumEdition != nil}
  /// Clears the value of `maximumEdition`. Subsequent reads from it will return its default value.
  public mutating func clearMaximumEdition() {self._maximumEdition = nil}

  public var unknownFields = UnknownStorage()

  /// A map from every known edition with a unique set of defaults to its
  /// defaults. Not all editions may be contained here.  For a given edition,
  /// the defaults at the closest matching edition ordered at or before it should
  /// be used.  This field must be in strict ascending order by edition.
  public struct FeatureSetEditionDefault: @unchecked Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    public var edition: Google_Protobuf_Edition {
      get {return _storage._edition ?? .unknown}
      set {_uniqueStorage()._edition = newValue}
    }
    /// Returns true if `edition` has been explicitly set.
    public var hasEdition: Bool {return _storage._edition != nil}
    /// Clears the value of `edition`. Subsequent reads from it will return its default value.
    public mutating func clearEdition() {_uniqueStorage()._edition = nil}

    /// Defaults of features that can be overridden in this edition.
    public var overridableFeatures: Google_Protobuf_FeatureSet {
      get {return _storage._overridableFeatures ?? Google_Protobuf_FeatureSet()}
      set {_uniqueStorage()._overridableFeatures = newValue}
    }
    /// Returns true if `overridableFeatures` has been explicitly set.
    public var hasOverridableFeatures: Bool {return _storage._overridableFeatures != nil}
    /// Clears the value of `overridableFeatures`. Subsequent reads from it will return its default value.
    public mutating func clearOverridableFeatures() {_uniqueStorage()._overridableFeatures = nil}

    /// Defaults of features that can't be overridden in this edition.
    public var fixedFeatures: Google_Protobuf_FeatureSet {
      get {return _storage._fixedFeatures ?? Google_Protobuf_FeatureSet()}
      set {_uniqueStorage()._fixedFeatures = newValue}
    }
    /// Returns true if `fixedFeatures` has been explicitly set.
    public var hasFixedFeatures: Bool {return _storage._fixedFeatures != nil}
    /// Clears the value of `fixedFeatures`. Subsequent reads from it will return its default value.
    public mutating func clearFixedFeatures() {_uniqueStorage()._fixedFeatures = nil}

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _storage = _StorageClass.defaultInstance
  }

  public init() {}

  fileprivate var _minimumEdition: Google_Protobuf_Edition? = nil
  fileprivate var _maximumEdition: Google_Protobuf_Edition? = nil
}

/// Encapsulates information about the original source file from which a
/// FileDescriptorProto was generated.
public struct Google_Protobuf_SourceCodeInfo: ExtensibleMessage, Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// A Location identifies a piece of source code in a .proto file which
  /// corresponds to a particular definition.  This information is intended
  /// to be useful to IDEs, code indexers, documentation generators, and similar
  /// tools.
  ///
  /// For example, say we have a file like:
  ///   message Foo {
  ///     optional string foo = 1;
  ///   }
  /// Let's look at just the field definition:
  ///   optional string foo = 1;
  ///   ^       ^^     ^^  ^  ^^^
  ///   a       bc     de  f  ghi
  /// We have the following locations:
  ///   span   path               represents
  ///   [a,i)  [ 4, 0, 2, 0 ]     The whole field definition.
  ///   [a,b)  [ 4, 0, 2, 0, 4 ]  The label (optional).
  ///   [c,d)  [ 4, 0, 2, 0, 5 ]  The type (string).
  ///   [e,f)  [ 4, 0, 2, 0, 1 ]  The name (foo).
  ///   [g,h)  [ 4, 0, 2, 0, 3 ]  The number (1).
  ///
  /// Notes:
  /// - A location may refer to a repeated field itself (i.e. not to any
  ///   particular index within it).  This is used whenever a set of elements are
  ///   logically enclosed in a single code segment.  For example, an entire
  ///   extend block (possibly containing multiple extension definitions) will
  ///   have an outer location whose path refers to the "extensions" repeated
  ///   field without an index.
  /// - Multiple locations may have the same path.  This happens when a single
  ///   logical declaration is spread out across multiple places.  The most
  ///   obvious example is the "extend" block again -- there may be multiple
  ///   extend blocks in the same scope, each of which will have the same path.
  /// - A location's span is not always a subset of its parent's span.  For
  ///   example, the "extendee" of an extension declaration appears at the
  ///   beginning of the "extend" block and is shared by all extensions within
  ///   the block.
  /// - Just because a location's span is a subset of some other location's span
  ///   does not mean that it is a descendant.  For example, a "group" defines
  ///   both a type and a field in a single declaration.  Thus, the locations
  ///   corresponding to the type and field and their components will overlap.
  /// - Code which tries to interpret locations should probably be designed to
  ///   ignore those that it doesn't understand, as more types of locations could
  ///   be recorded in the future.
  public var location: [Google_Protobuf_SourceCodeInfo.Location] = []

  public var unknownFields = UnknownStorage()

  public struct Location: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// Identifies which part of the FileDescriptorProto was defined at this
    /// location.
    ///
    /// Each element is a field number or an index.  They form a path from
    /// the root FileDescriptorProto to the place where the definition appears.
    /// For example, this path:
    ///   [ 4, 3, 2, 7, 1 ]
    /// refers to:
    ///   file.message_type(3)  // 4, 3
    ///       .field(7)         // 2, 7
    ///       .name()           // 1
    /// This is because FileDescriptorProto.message_type has field number 4:
    ///   repeated DescriptorProto message_type = 4;
    /// and DescriptorProto.field has field number 2:
    ///   repeated FieldDescriptorProto field = 2;
    /// and FieldDescriptorProto.name has field number 1:
    ///   optional string name = 1;
    ///
    /// Thus, the above path gives the location of a field name.  If we removed
    /// the last element:
    ///   [ 4, 3, 2, 7 ]
    /// this path refers to the whole field declaration (from the beginning
    /// of the label to the terminating semicolon).
    public var path: [Int32] = []

    /// Always has exactly three or four elements: start line, start column,
    /// end line (optional, otherwise assumed same as start line), end column.
    /// These are packed into a single field for efficiency.  Note that line
    /// and column numbers are zero-based -- typically you will want to add
    /// 1 to each before displaying to a user.
    public var span: [Int32] = []

    /// If this SourceCodeInfo represents a complete declaration, these are any
    /// comments appearing before and after the declaration which appear to be
    /// attached to the declaration.
    ///
    /// A series of line comments appearing on consecutive lines, with no other
    /// tokens appearing on those lines, will be treated as a single comment.
    ///
    /// leading_detached_comments will keep paragraphs of comments that appear
    /// before (but not connected to) the current element. Each paragraph,
    /// separated by empty lines, will be one comment element in the repeated
    /// field.
    ///
    /// Only the comment content is provided; comment markers (e.g. //) are
    /// stripped out.  For block comments, leading whitespace and an asterisk
    /// will be stripped from the beginning of each line other than the first.
    /// Newlines are included in the output.
    ///
    /// Examples:
    ///
    ///   optional int32 foo = 1;  // Comment attached to foo.
    ///   // Comment attached to bar.
    ///   optional int32 bar = 2;
    ///
    ///   optional string baz = 3;
    ///   // Comment attached to baz.
    ///   // Another line attached to baz.
    ///
    ///   // Comment attached to moo.
    ///   //
    ///   // Another line attached to moo.
    ///   optional double moo = 4;
    ///
    ///   // Detached comment for corge. This is not leading or trailing comments
    ///   // to moo or corge because there are blank lines separating it from
    ///   // both.
    ///
    ///   // Detached comment for corge paragraph 2.
    ///
    ///   optional string corge = 5;
    ///   /* Block comment attached
    ///    * to corge.  Leading asterisks
    ///    * will be removed. */
    ///   /* Block comment attached to
    ///    * grault. */
    ///   optional int32 grault = 6;
    ///
    ///   // ignored detached comments.
    public var leadingComments: String {
      get {return _leadingComments ?? String()}
      set {_leadingComments = newValue}
    }
    /// Returns true if `leadingComments` has been explicitly set.
    public var hasLeadingComments: Bool {return self._leadingComments != nil}
    /// Clears the value of `leadingComments`. Subsequent reads from it will return its default value.
    public mutating func clearLeadingComments() {self._leadingComments = nil}

    public var trailingComments: String {
      get {return _trailingComments ?? String()}
      set {_trailingComments = newValue}
    }
    /// Returns true if `trailingComments` has been explicitly set.
    public var hasTrailingComments: Bool {return self._trailingComments != nil}
    /// Clears the value of `trailingComments`. Subsequent reads from it will return its default value.
    public mutating func clearTrailingComments() {self._trailingComments = nil}

    public var leadingDetachedComments: [String] = []

    public var unknownFields = UnknownStorage()

    public init() {}

    fileprivate var _leadingComments: String? = nil
    fileprivate var _trailingComments: String? = nil
  }

  public init() {}

  public var _protobuf_extensionFieldValues = ExtensionFieldValueSet()
}

/// Describes the relationship between generated code and its original source
/// file. A GeneratedCodeInfo message is associated with only one generated
/// source file, but may contain references to different source .proto files.
public struct Google_Protobuf_GeneratedCodeInfo: Sendable {
  // SwiftProtobuf.Message conformance is added in an extension below. See the
  // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
  // methods supported on all messages.

  /// An Annotation connects some span of text in generated code to an element
  /// of its generating .proto file.
  public var annotation: [Google_Protobuf_GeneratedCodeInfo.Annotation] = []

  public var unknownFields = UnknownStorage()

  public struct Annotation: Sendable {
    // SwiftProtobuf.Message conformance is added in an extension below. See the
    // `Message` and `Message+*Additions` files in the SwiftProtobuf library for
    // methods supported on all messages.

    /// Identifies the element in the original source .proto file. This field
    /// is formatted the same as SourceCodeInfo.Location.path.
    public var path: [Int32] = []

    /// Identifies the filesystem path to the original source .proto.
    public var sourceFile: String {
      get {return _sourceFile ?? String()}
      set {_sourceFile = newValue}
    }
    /// Returns true if `sourceFile` has been explicitly set.
    public var hasSourceFile: Bool {return self._sourceFile != nil}
    /// Clears the value of `sourceFile`. Subsequent reads from it will return its default value.
    public mutating func clearSourceFile() {self._sourceFile = nil}

    /// Identifies the starting offset in bytes in the generated code
    /// that relates to the identified object.
    public var begin: Int32 {
      get {return _begin ?? 0}
      set {_begin = newValue}
    }
    /// Returns true if `begin` has been explicitly set.
    public var hasBegin: Bool {return self._begin != nil}
    /// Clears the value of `begin`. Subsequent reads from it will return its default value.
    public mutating func clearBegin() {self._begin = nil}

    /// Identifies the ending offset in bytes in the generated code that
    /// relates to the identified object. The end offset should be one past
    /// the last relevant byte (so the length of the text = end - begin).
    public var end: Int32 {
      get {return _end ?? 0}
      set {_end = newValue}
    }
    /// Returns true if `end` has been explicitly set.
    public var hasEnd: Bool {return self._end != nil}
    /// Clears the value of `end`. Subsequent reads from it will return its default value.
    public mutating func clearEnd() {self._end = nil}

    public var semantic: Google_Protobuf_GeneratedCodeInfo.Annotation.Semantic {
      get {return _semantic ?? .none}
      set {_semantic = newValue}
    }
    /// Returns true if `semantic` has been explicitly set.
    public var hasSemantic: Bool {return self._semantic != nil}
    /// Clears the value of `semantic`. Subsequent reads from it will return its default value.
    public mutating func clearSemantic() {self._semantic = nil}

    public var unknownFields = UnknownStorage()

    /// Represents the identified object's effect on the element in the original
    /// .proto file.
    public enum Semantic: Int, Enum, Swift.CaseIterable {

      /// There is no effect or the effect is indescribable.
      case none = 0

      /// The element is set or otherwise mutated.
      case set = 1

      /// An alias to the element is returned.
      case alias = 2

      public init() {
        self = .none
      }

    }

    public init() {}

    fileprivate var _sourceFile: String? = nil
    fileprivate var _begin: Int32? = nil
    fileprivate var _end: Int32? = nil
    fileprivate var _semantic: Google_Protobuf_GeneratedCodeInfo.Annotation.Semantic? = nil
  }

  public init() {}
}

// MARK: - Code below here is support for the SwiftProtobuf runtime.

fileprivate let _protobuf_package = "google.protobuf"

extension Google_Protobuf_Edition: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "EDITION_UNKNOWN"),
    1: .same(proto: "EDITION_1_TEST_ONLY"),
    2: .same(proto: "EDITION_2_TEST_ONLY"),
    900: .same(proto: "EDITION_LEGACY"),
    998: .same(proto: "EDITION_PROTO2"),
    999: .same(proto: "EDITION_PROTO3"),
    1000: .same(proto: "EDITION_2023"),
    1001: .same(proto: "EDITION_2024"),
    99997: .same(proto: "EDITION_99997_TEST_ONLY"),
    99998: .same(proto: "EDITION_99998_TEST_ONLY"),
    99999: .same(proto: "EDITION_99999_TEST_ONLY"),
    2147483647: .same(proto: "EDITION_MAX"),
  ]
}

extension Google_Protobuf_SymbolVisibility: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "VISIBILITY_UNSET"),
    1: .same(proto: "VISIBILITY_LOCAL"),
    2: .same(proto: "VISIBILITY_EXPORT"),
  ]
}

extension Google_Protobuf_FileDescriptorSet: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FileDescriptorSet"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "file"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if !Internal.areAllInitialized(self.file) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.file) }()
      case 536000000:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_FileDescriptorSet.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if !self.file.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.file, fieldNumber: 1)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 536000000, end: 536000001)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FileDescriptorSet, rhs: Google_Protobuf_FileDescriptorSet) -> Bool {
    if lhs.file != rhs.file {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_FileDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FileDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "package"),
    3: .same(proto: "dependency"),
    10: .standard(proto: "public_dependency"),
    11: .standard(proto: "weak_dependency"),
    15: .standard(proto: "option_dependency"),
    4: .standard(proto: "message_type"),
    5: .standard(proto: "enum_type"),
    6: .same(proto: "service"),
    7: .same(proto: "extension"),
    8: .same(proto: "options"),
    9: .standard(proto: "source_code_info"),
    12: .same(proto: "syntax"),
    14: .same(proto: "edition"),
  ]

  public var isInitialized: Bool {
    if !Internal.areAllInitialized(self.messageType) {return false}
    if !Internal.areAllInitialized(self.enumType) {return false}
    if !Internal.areAllInitialized(self.service) {return false}
    if !Internal.areAllInitialized(self.`extension`) {return false}
    if let v = self._options, !v.isInitialized {return false}
    if let v = self._sourceCodeInfo, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self._name) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self._package) }()
      case 3: try { try decoder.decodeRepeatedStringField(value: &self.dependency) }()
      case 4: try { try decoder.decodeRepeatedMessageField(value: &self.messageType) }()
      case 5: try { try decoder.decodeRepeatedMessageField(value: &self.enumType) }()
      case 6: try { try decoder.decodeRepeatedMessageField(value: &self.service) }()
      case 7: try { try decoder.decodeRepeatedMessageField(value: &self.`extension`) }()
      case 8: try { try decoder.decodeSingularMessageField(value: &self._options) }()
      case 9: try { try decoder.decodeSingularMessageField(value: &self._sourceCodeInfo) }()
      case 10: try { try decoder.decodeRepeatedInt32Field(value: &self.publicDependency) }()
      case 11: try { try decoder.decodeRepeatedInt32Field(value: &self.weakDependency) }()
      case 12: try { try decoder.decodeSingularStringField(value: &self._syntax) }()
      case 14: try { try decoder.decodeSingularEnumField(value: &self._edition) }()
      case 15: try { try decoder.decodeRepeatedStringField(value: &self.optionDependency) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._name {
      try visitor.visitSingularStringField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._package {
      try visitor.visitSingularStringField(value: v, fieldNumber: 2)
    } }()
    if !self.dependency.isEmpty {
      try visitor.visitRepeatedStringField(value: self.dependency, fieldNumber: 3)
    }
    if !self.messageType.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.messageType, fieldNumber: 4)
    }
    if !self.enumType.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.enumType, fieldNumber: 5)
    }
    if !self.service.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.service, fieldNumber: 6)
    }
    if !self.`extension`.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.`extension`, fieldNumber: 7)
    }
    try { if let v = self._options {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
    } }()
    try { if let v = self._sourceCodeInfo {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 9)
    } }()
    if !self.publicDependency.isEmpty {
      try visitor.visitRepeatedInt32Field(value: self.publicDependency, fieldNumber: 10)
    }
    if !self.weakDependency.isEmpty {
      try visitor.visitRepeatedInt32Field(value: self.weakDependency, fieldNumber: 11)
    }
    try { if let v = self._syntax {
      try visitor.visitSingularStringField(value: v, fieldNumber: 12)
    } }()
    try { if let v = self._edition {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 14)
    } }()
    if !self.optionDependency.isEmpty {
      try visitor.visitRepeatedStringField(value: self.optionDependency, fieldNumber: 15)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FileDescriptorProto, rhs: Google_Protobuf_FileDescriptorProto) -> Bool {
    if lhs._name != rhs._name {return false}
    if lhs._package != rhs._package {return false}
    if lhs.dependency != rhs.dependency {return false}
    if lhs.publicDependency != rhs.publicDependency {return false}
    if lhs.weakDependency != rhs.weakDependency {return false}
    if lhs.optionDependency != rhs.optionDependency {return false}
    if lhs.messageType != rhs.messageType {return false}
    if lhs.enumType != rhs.enumType {return false}
    if lhs.service != rhs.service {return false}
    if lhs.`extension` != rhs.`extension` {return false}
    if lhs._options != rhs._options {return false}
    if lhs._sourceCodeInfo != rhs._sourceCodeInfo {return false}
    if lhs._syntax != rhs._syntax {return false}
    if lhs._edition != rhs._edition {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_DescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".DescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "field"),
    6: .same(proto: "extension"),
    3: .standard(proto: "nested_type"),
    4: .standard(proto: "enum_type"),
    5: .standard(proto: "extension_range"),
    8: .standard(proto: "oneof_decl"),
    7: .same(proto: "options"),
    9: .standard(proto: "reserved_range"),
    10: .standard(proto: "reserved_name"),
    11: .same(proto: "visibility"),
  ]

  fileprivate class _StorageClass {
    var _name: String? = nil
    var _field: [Google_Protobuf_FieldDescriptorProto] = []
    var _extension: [Google_Protobuf_FieldDescriptorProto] = []
    var _nestedType: [Google_Protobuf_DescriptorProto] = []
    var _enumType: [Google_Protobuf_EnumDescriptorProto] = []
    var _extensionRange: [Google_Protobuf_DescriptorProto.ExtensionRange] = []
    var _oneofDecl: [Google_Protobuf_OneofDescriptorProto] = []
    var _options: Google_Protobuf_MessageOptions? = nil
    var _reservedRange: [Google_Protobuf_DescriptorProto.ReservedRange] = []
    var _reservedName: [String] = []
    var _visibility: Google_Protobuf_SymbolVisibility? = nil

      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _name = source._name
      _field = source._field
      _extension = source._extension
      _nestedType = source._nestedType
      _enumType = source._enumType
      _extensionRange = source._extensionRange
      _oneofDecl = source._oneofDecl
      _options = source._options
      _reservedRange = source._reservedRange
      _reservedName = source._reservedName
      _visibility = source._visibility
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public var isInitialized: Bool {
    return withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      if !Internal.areAllInitialized(_storage._field) {return false}
      if !Internal.areAllInitialized(_storage._extension) {return false}
      if !Internal.areAllInitialized(_storage._nestedType) {return false}
      if !Internal.areAllInitialized(_storage._enumType) {return false}
      if !Internal.areAllInitialized(_storage._extensionRange) {return false}
      if !Internal.areAllInitialized(_storage._oneofDecl) {return false}
      if let v = _storage._options, !v.isInitialized {return false}
      return true
    }
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularStringField(value: &_storage._name) }()
        case 2: try { try decoder.decodeRepeatedMessageField(value: &_storage._field) }()
        case 3: try { try decoder.decodeRepeatedMessageField(value: &_storage._nestedType) }()
        case 4: try { try decoder.decodeRepeatedMessageField(value: &_storage._enumType) }()
        case 5: try { try decoder.decodeRepeatedMessageField(value: &_storage._extensionRange) }()
        case 6: try { try decoder.decodeRepeatedMessageField(value: &_storage._extension) }()
        case 7: try { try decoder.decodeSingularMessageField(value: &_storage._options) }()
        case 8: try { try decoder.decodeRepeatedMessageField(value: &_storage._oneofDecl) }()
        case 9: try { try decoder.decodeRepeatedMessageField(value: &_storage._reservedRange) }()
        case 10: try { try decoder.decodeRepeatedStringField(value: &_storage._reservedName) }()
        case 11: try { try decoder.decodeSingularEnumField(value: &_storage._visibility) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._name {
        try visitor.visitSingularStringField(value: v, fieldNumber: 1)
      } }()
      if !_storage._field.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._field, fieldNumber: 2)
      }
      if !_storage._nestedType.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._nestedType, fieldNumber: 3)
      }
      if !_storage._enumType.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._enumType, fieldNumber: 4)
      }
      if !_storage._extensionRange.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._extensionRange, fieldNumber: 5)
      }
      if !_storage._extension.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._extension, fieldNumber: 6)
      }
      try { if let v = _storage._options {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
      } }()
      if !_storage._oneofDecl.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._oneofDecl, fieldNumber: 8)
      }
      if !_storage._reservedRange.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._reservedRange, fieldNumber: 9)
      }
      if !_storage._reservedName.isEmpty {
        try visitor.visitRepeatedStringField(value: _storage._reservedName, fieldNumber: 10)
      }
      try { if let v = _storage._visibility {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 11)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_DescriptorProto, rhs: Google_Protobuf_DescriptorProto) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._name != rhs_storage._name {return false}
        if _storage._field != rhs_storage._field {return false}
        if _storage._extension != rhs_storage._extension {return false}
        if _storage._nestedType != rhs_storage._nestedType {return false}
        if _storage._enumType != rhs_storage._enumType {return false}
        if _storage._extensionRange != rhs_storage._extensionRange {return false}
        if _storage._oneofDecl != rhs_storage._oneofDecl {return false}
        if _storage._options != rhs_storage._options {return false}
        if _storage._reservedRange != rhs_storage._reservedRange {return false}
        if _storage._reservedName != rhs_storage._reservedName {return false}
        if _storage._visibility != rhs_storage._visibility {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_DescriptorProto.ExtensionRange: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_DescriptorProto.protoMessageName + ".ExtensionRange"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "start"),
    2: .same(proto: "end"),
    3: .same(proto: "options"),
  ]

  public var isInitialized: Bool {
    if let v = self._options, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self._start) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self._end) }()
      case 3: try { try decoder.decodeSingularMessageField(value: &self._options) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._start {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._end {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._options {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_DescriptorProto.ExtensionRange, rhs: Google_Protobuf_DescriptorProto.ExtensionRange) -> Bool {
    if lhs._start != rhs._start {return false}
    if lhs._end != rhs._end {return false}
    if lhs._options != rhs._options {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_DescriptorProto.ReservedRange: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_DescriptorProto.protoMessageName + ".ReservedRange"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "start"),
    2: .same(proto: "end"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self._start) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self._end) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._start {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._end {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 2)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_DescriptorProto.ReservedRange, rhs: Google_Protobuf_DescriptorProto.ReservedRange) -> Bool {
    if lhs._start != rhs._start {return false}
    if lhs._end != rhs._end {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_ExtensionRangeOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".ExtensionRangeOptions"
  public static let _protobuf_nameMap: _NameMap = [
    999: .standard(proto: "uninterpreted_option"),
    2: .same(proto: "declaration"),
    50: .same(proto: "features"),
    3: .same(proto: "verification"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    if let v = self._features, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.declaration) }()
      case 3: try { try decoder.decodeSingularEnumField(value: &self._verification) }()
      case 50: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_ExtensionRangeOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.declaration.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.declaration, fieldNumber: 2)
    }
    try { if let v = self._verification {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 50)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_ExtensionRangeOptions, rhs: Google_Protobuf_ExtensionRangeOptions) -> Bool {
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.declaration != rhs.declaration {return false}
    if lhs._features != rhs._features {return false}
    if lhs._verification != rhs._verification {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_ExtensionRangeOptions.VerificationState: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "DECLARATION"),
    1: .same(proto: "UNVERIFIED"),
  ]
}

extension Google_Protobuf_ExtensionRangeOptions.Declaration: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_ExtensionRangeOptions.protoMessageName + ".Declaration"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: [],
      reservedRanges: [4..<5],
      numberNameMappings: [
        1: .same(proto: "number"),
        2: .standard(proto: "full_name"),
        3: .same(proto: "type"),
        5: .same(proto: "reserved"),
        6: .same(proto: "repeated"),
  ])

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self._number) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self._fullName) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self._type) }()
      case 5: try { try decoder.decodeSingularBoolField(value: &self._reserved) }()
      case 6: try { try decoder.decodeSingularBoolField(value: &self._repeated) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._number {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._fullName {
      try visitor.visitSingularStringField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._type {
      try visitor.visitSingularStringField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._reserved {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 5)
    } }()
    try { if let v = self._repeated {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 6)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_ExtensionRangeOptions.Declaration, rhs: Google_Protobuf_ExtensionRangeOptions.Declaration) -> Bool {
    if lhs._number != rhs._number {return false}
    if lhs._fullName != rhs._fullName {return false}
    if lhs._type != rhs._type {return false}
    if lhs._reserved != rhs._reserved {return false}
    if lhs._repeated != rhs._repeated {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FieldDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FieldDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    3: .same(proto: "number"),
    4: .same(proto: "label"),
    5: .same(proto: "type"),
    6: .standard(proto: "type_name"),
    2: .same(proto: "extendee"),
    7: .standard(proto: "default_value"),
    9: .standard(proto: "oneof_index"),
    10: .standard(proto: "json_name"),
    8: .same(proto: "options"),
    17: .standard(proto: "proto3_optional"),
  ]

  public var isInitialized: Bool {
    if let v = self._options, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self._name) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self._extendee) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self._number) }()
      case 4: try { try decoder.decodeSingularEnumField(value: &self._label) }()
      case 5: try { try decoder.decodeSingularEnumField(value: &self._type) }()
      case 6: try { try decoder.decodeSingularStringField(value: &self._typeName) }()
      case 7: try { try decoder.decodeSingularStringField(value: &self._defaultValue) }()
      case 8: try { try decoder.decodeSingularMessageField(value: &self._options) }()
      case 9: try { try decoder.decodeSingularInt32Field(value: &self._oneofIndex) }()
      case 10: try { try decoder.decodeSingularStringField(value: &self._jsonName) }()
      case 17: try { try decoder.decodeSingularBoolField(value: &self._proto3Optional) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._name {
      try visitor.visitSingularStringField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._extendee {
      try visitor.visitSingularStringField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._number {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._label {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 4)
    } }()
    try { if let v = self._type {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 5)
    } }()
    try { if let v = self._typeName {
      try visitor.visitSingularStringField(value: v, fieldNumber: 6)
    } }()
    try { if let v = self._defaultValue {
      try visitor.visitSingularStringField(value: v, fieldNumber: 7)
    } }()
    try { if let v = self._options {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 8)
    } }()
    try { if let v = self._oneofIndex {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 9)
    } }()
    try { if let v = self._jsonName {
      try visitor.visitSingularStringField(value: v, fieldNumber: 10)
    } }()
    try { if let v = self._proto3Optional {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 17)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FieldDescriptorProto, rhs: Google_Protobuf_FieldDescriptorProto) -> Bool {
    if lhs._name != rhs._name {return false}
    if lhs._number != rhs._number {return false}
    if lhs._label != rhs._label {return false}
    if lhs._type != rhs._type {return false}
    if lhs._typeName != rhs._typeName {return false}
    if lhs._extendee != rhs._extendee {return false}
    if lhs._defaultValue != rhs._defaultValue {return false}
    if lhs._oneofIndex != rhs._oneofIndex {return false}
    if lhs._jsonName != rhs._jsonName {return false}
    if lhs._options != rhs._options {return false}
    if lhs._proto3Optional != rhs._proto3Optional {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FieldDescriptorProto.TypeEnum: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "TYPE_DOUBLE"),
    2: .same(proto: "TYPE_FLOAT"),
    3: .same(proto: "TYPE_INT64"),
    4: .same(proto: "TYPE_UINT64"),
    5: .same(proto: "TYPE_INT32"),
    6: .same(proto: "TYPE_FIXED64"),
    7: .same(proto: "TYPE_FIXED32"),
    8: .same(proto: "TYPE_BOOL"),
    9: .same(proto: "TYPE_STRING"),
    10: .same(proto: "TYPE_GROUP"),
    11: .same(proto: "TYPE_MESSAGE"),
    12: .same(proto: "TYPE_BYTES"),
    13: .same(proto: "TYPE_UINT32"),
    14: .same(proto: "TYPE_ENUM"),
    15: .same(proto: "TYPE_SFIXED32"),
    16: .same(proto: "TYPE_SFIXED64"),
    17: .same(proto: "TYPE_SINT32"),
    18: .same(proto: "TYPE_SINT64"),
  ]
}

extension Google_Protobuf_FieldDescriptorProto.Label: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "LABEL_OPTIONAL"),
    2: .same(proto: "LABEL_REQUIRED"),
    3: .same(proto: "LABEL_REPEATED"),
  ]
}

extension Google_Protobuf_OneofDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".OneofDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "options"),
  ]

  public var isInitialized: Bool {
    if let v = self._options, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self._name) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._options) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._name {
      try visitor.visitSingularStringField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._options {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_OneofDescriptorProto, rhs: Google_Protobuf_OneofDescriptorProto) -> Bool {
    if lhs._name != rhs._name {return false}
    if lhs._options != rhs._options {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_EnumDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".EnumDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "value"),
    3: .same(proto: "options"),
    4: .standard(proto: "reserved_range"),
    5: .standard(proto: "reserved_name"),
    6: .same(proto: "visibility"),
  ]

  fileprivate class _StorageClass {
    var _name: String? = nil
    var _value: [Google_Protobuf_EnumValueDescriptorProto] = []
    var _options: Google_Protobuf_EnumOptions? = nil
    var _reservedRange: [Google_Protobuf_EnumDescriptorProto.EnumReservedRange] = []
    var _reservedName: [String] = []
    var _visibility: Google_Protobuf_SymbolVisibility? = nil

      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _name = source._name
      _value = source._value
      _options = source._options
      _reservedRange = source._reservedRange
      _reservedName = source._reservedName
      _visibility = source._visibility
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public var isInitialized: Bool {
    return withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      if !Internal.areAllInitialized(_storage._value) {return false}
      if let v = _storage._options, !v.isInitialized {return false}
      return true
    }
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularStringField(value: &_storage._name) }()
        case 2: try { try decoder.decodeRepeatedMessageField(value: &_storage._value) }()
        case 3: try { try decoder.decodeSingularMessageField(value: &_storage._options) }()
        case 4: try { try decoder.decodeRepeatedMessageField(value: &_storage._reservedRange) }()
        case 5: try { try decoder.decodeRepeatedStringField(value: &_storage._reservedName) }()
        case 6: try { try decoder.decodeSingularEnumField(value: &_storage._visibility) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._name {
        try visitor.visitSingularStringField(value: v, fieldNumber: 1)
      } }()
      if !_storage._value.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._value, fieldNumber: 2)
      }
      try { if let v = _storage._options {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
      } }()
      if !_storage._reservedRange.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._reservedRange, fieldNumber: 4)
      }
      if !_storage._reservedName.isEmpty {
        try visitor.visitRepeatedStringField(value: _storage._reservedName, fieldNumber: 5)
      }
      try { if let v = _storage._visibility {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 6)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_EnumDescriptorProto, rhs: Google_Protobuf_EnumDescriptorProto) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._name != rhs_storage._name {return false}
        if _storage._value != rhs_storage._value {return false}
        if _storage._options != rhs_storage._options {return false}
        if _storage._reservedRange != rhs_storage._reservedRange {return false}
        if _storage._reservedName != rhs_storage._reservedName {return false}
        if _storage._visibility != rhs_storage._visibility {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_EnumDescriptorProto.EnumReservedRange: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_EnumDescriptorProto.protoMessageName + ".EnumReservedRange"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "start"),
    2: .same(proto: "end"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularInt32Field(value: &self._start) }()
      case 2: try { try decoder.decodeSingularInt32Field(value: &self._end) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._start {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._end {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 2)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_EnumDescriptorProto.EnumReservedRange, rhs: Google_Protobuf_EnumDescriptorProto.EnumReservedRange) -> Bool {
    if lhs._start != rhs._start {return false}
    if lhs._end != rhs._end {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_EnumValueDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".EnumValueDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "number"),
    3: .same(proto: "options"),
  ]

  fileprivate class _StorageClass {
    var _name: String? = nil
    var _number: Int32? = nil
    var _options: Google_Protobuf_EnumValueOptions? = nil

      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _name = source._name
      _number = source._number
      _options = source._options
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public var isInitialized: Bool {
    return withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      if let v = _storage._options, !v.isInitialized {return false}
      return true
    }
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularStringField(value: &_storage._name) }()
        case 2: try { try decoder.decodeSingularInt32Field(value: &_storage._number) }()
        case 3: try { try decoder.decodeSingularMessageField(value: &_storage._options) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._name {
        try visitor.visitSingularStringField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._number {
        try visitor.visitSingularInt32Field(value: v, fieldNumber: 2)
      } }()
      try { if let v = _storage._options {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_EnumValueDescriptorProto, rhs: Google_Protobuf_EnumValueDescriptorProto) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._name != rhs_storage._name {return false}
        if _storage._number != rhs_storage._number {return false}
        if _storage._options != rhs_storage._options {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_ServiceDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".ServiceDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .same(proto: "method"),
    3: .same(proto: "options"),
  ]

  public var isInitialized: Bool {
    if !Internal.areAllInitialized(self.method) {return false}
    if let v = self._options, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self._name) }()
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.method) }()
      case 3: try { try decoder.decodeSingularMessageField(value: &self._options) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._name {
      try visitor.visitSingularStringField(value: v, fieldNumber: 1)
    } }()
    if !self.method.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.method, fieldNumber: 2)
    }
    try { if let v = self._options {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 3)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_ServiceDescriptorProto, rhs: Google_Protobuf_ServiceDescriptorProto) -> Bool {
    if lhs._name != rhs._name {return false}
    if lhs.method != rhs.method {return false}
    if lhs._options != rhs._options {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_MethodDescriptorProto: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".MethodDescriptorProto"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "name"),
    2: .standard(proto: "input_type"),
    3: .standard(proto: "output_type"),
    4: .same(proto: "options"),
    5: .standard(proto: "client_streaming"),
    6: .standard(proto: "server_streaming"),
  ]

  public var isInitialized: Bool {
    if let v = self._options, !v.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self._name) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self._inputType) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self._outputType) }()
      case 4: try { try decoder.decodeSingularMessageField(value: &self._options) }()
      case 5: try { try decoder.decodeSingularBoolField(value: &self._clientStreaming) }()
      case 6: try { try decoder.decodeSingularBoolField(value: &self._serverStreaming) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._name {
      try visitor.visitSingularStringField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._inputType {
      try visitor.visitSingularStringField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._outputType {
      try visitor.visitSingularStringField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._options {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
    } }()
    try { if let v = self._clientStreaming {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 5)
    } }()
    try { if let v = self._serverStreaming {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 6)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_MethodDescriptorProto, rhs: Google_Protobuf_MethodDescriptorProto) -> Bool {
    if lhs._name != rhs._name {return false}
    if lhs._inputType != rhs._inputType {return false}
    if lhs._outputType != rhs._outputType {return false}
    if lhs._options != rhs._options {return false}
    if lhs._clientStreaming != rhs._clientStreaming {return false}
    if lhs._serverStreaming != rhs._serverStreaming {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FileOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FileOptions"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: ["php_generic_services"],
      reservedRanges: [38..<39, 42..<43],
      numberNameMappings: [
        1: .standard(proto: "java_package"),
        8: .standard(proto: "java_outer_classname"),
        10: .standard(proto: "java_multiple_files"),
        20: .standard(proto: "java_generate_equals_and_hash"),
        27: .standard(proto: "java_string_check_utf8"),
        9: .standard(proto: "optimize_for"),
        11: .standard(proto: "go_package"),
        16: .standard(proto: "cc_generic_services"),
        17: .standard(proto: "java_generic_services"),
        18: .standard(proto: "py_generic_services"),
        23: .same(proto: "deprecated"),
        31: .standard(proto: "cc_enable_arenas"),
        36: .standard(proto: "objc_class_prefix"),
        37: .standard(proto: "csharp_namespace"),
        39: .standard(proto: "swift_prefix"),
        40: .standard(proto: "php_class_prefix"),
        41: .standard(proto: "php_namespace"),
        44: .standard(proto: "php_metadata_namespace"),
        45: .standard(proto: "ruby_package"),
        50: .same(proto: "features"),
        999: .standard(proto: "uninterpreted_option"),
  ])

  fileprivate class _StorageClass {
    var _javaPackage: String? = nil
    var _javaOuterClassname: String? = nil
    var _javaMultipleFiles: Bool? = nil
    var _javaGenerateEqualsAndHash: Bool? = nil
    var _javaStringCheckUtf8: Bool? = nil
    var _optimizeFor: Google_Protobuf_FileOptions.OptimizeMode? = nil
    var _goPackage: String? = nil
    var _ccGenericServices: Bool? = nil
    var _javaGenericServices: Bool? = nil
    var _pyGenericServices: Bool? = nil
    var _deprecated: Bool? = nil
    var _ccEnableArenas: Bool? = nil
    var _objcClassPrefix: String? = nil
    var _csharpNamespace: String? = nil
    var _swiftPrefix: String? = nil
    var _phpClassPrefix: String? = nil
    var _phpNamespace: String? = nil
    var _phpMetadataNamespace: String? = nil
    var _rubyPackage: String? = nil
    var _features: Google_Protobuf_FeatureSet? = nil
    var _uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _javaPackage = source._javaPackage
      _javaOuterClassname = source._javaOuterClassname
      _javaMultipleFiles = source._javaMultipleFiles
      _javaGenerateEqualsAndHash = source._javaGenerateEqualsAndHash
      _javaStringCheckUtf8 = source._javaStringCheckUtf8
      _optimizeFor = source._optimizeFor
      _goPackage = source._goPackage
      _ccGenericServices = source._ccGenericServices
      _javaGenericServices = source._javaGenericServices
      _pyGenericServices = source._pyGenericServices
      _deprecated = source._deprecated
      _ccEnableArenas = source._ccEnableArenas
      _objcClassPrefix = source._objcClassPrefix
      _csharpNamespace = source._csharpNamespace
      _swiftPrefix = source._swiftPrefix
      _phpClassPrefix = source._phpClassPrefix
      _phpNamespace = source._phpNamespace
      _phpMetadataNamespace = source._phpMetadataNamespace
      _rubyPackage = source._rubyPackage
      _features = source._features
      _uninterpretedOption = source._uninterpretedOption
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    return withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      if let v = _storage._features, !v.isInitialized {return false}
      if !Internal.areAllInitialized(_storage._uninterpretedOption) {return false}
      return true
    }
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularStringField(value: &_storage._javaPackage) }()
        case 8: try { try decoder.decodeSingularStringField(value: &_storage._javaOuterClassname) }()
        case 9: try { try decoder.decodeSingularEnumField(value: &_storage._optimizeFor) }()
        case 10: try { try decoder.decodeSingularBoolField(value: &_storage._javaMultipleFiles) }()
        case 11: try { try decoder.decodeSingularStringField(value: &_storage._goPackage) }()
        case 16: try { try decoder.decodeSingularBoolField(value: &_storage._ccGenericServices) }()
        case 17: try { try decoder.decodeSingularBoolField(value: &_storage._javaGenericServices) }()
        case 18: try { try decoder.decodeSingularBoolField(value: &_storage._pyGenericServices) }()
        case 20: try { try decoder.decodeSingularBoolField(value: &_storage._javaGenerateEqualsAndHash) }()
        case 23: try { try decoder.decodeSingularBoolField(value: &_storage._deprecated) }()
        case 27: try { try decoder.decodeSingularBoolField(value: &_storage._javaStringCheckUtf8) }()
        case 31: try { try decoder.decodeSingularBoolField(value: &_storage._ccEnableArenas) }()
        case 36: try { try decoder.decodeSingularStringField(value: &_storage._objcClassPrefix) }()
        case 37: try { try decoder.decodeSingularStringField(value: &_storage._csharpNamespace) }()
        case 39: try { try decoder.decodeSingularStringField(value: &_storage._swiftPrefix) }()
        case 40: try { try decoder.decodeSingularStringField(value: &_storage._phpClassPrefix) }()
        case 41: try { try decoder.decodeSingularStringField(value: &_storage._phpNamespace) }()
        case 44: try { try decoder.decodeSingularStringField(value: &_storage._phpMetadataNamespace) }()
        case 45: try { try decoder.decodeSingularStringField(value: &_storage._rubyPackage) }()
        case 50: try { try decoder.decodeSingularMessageField(value: &_storage._features) }()
        case 999: try { try decoder.decodeRepeatedMessageField(value: &_storage._uninterpretedOption) }()
        case 1000..<536870912:
          try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_FileOptions.self, fieldNumber: fieldNumber) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._javaPackage {
        try visitor.visitSingularStringField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._javaOuterClassname {
        try visitor.visitSingularStringField(value: v, fieldNumber: 8)
      } }()
      try { if let v = _storage._optimizeFor {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 9)
      } }()
      try { if let v = _storage._javaMultipleFiles {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 10)
      } }()
      try { if let v = _storage._goPackage {
        try visitor.visitSingularStringField(value: v, fieldNumber: 11)
      } }()
      try { if let v = _storage._ccGenericServices {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 16)
      } }()
      try { if let v = _storage._javaGenericServices {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 17)
      } }()
      try { if let v = _storage._pyGenericServices {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 18)
      } }()
      try { if let v = _storage._javaGenerateEqualsAndHash {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 20)
      } }()
      try { if let v = _storage._deprecated {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 23)
      } }()
      try { if let v = _storage._javaStringCheckUtf8 {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 27)
      } }()
      try { if let v = _storage._ccEnableArenas {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 31)
      } }()
      try { if let v = _storage._objcClassPrefix {
        try visitor.visitSingularStringField(value: v, fieldNumber: 36)
      } }()
      try { if let v = _storage._csharpNamespace {
        try visitor.visitSingularStringField(value: v, fieldNumber: 37)
      } }()
      try { if let v = _storage._swiftPrefix {
        try visitor.visitSingularStringField(value: v, fieldNumber: 39)
      } }()
      try { if let v = _storage._phpClassPrefix {
        try visitor.visitSingularStringField(value: v, fieldNumber: 40)
      } }()
      try { if let v = _storage._phpNamespace {
        try visitor.visitSingularStringField(value: v, fieldNumber: 41)
      } }()
      try { if let v = _storage._phpMetadataNamespace {
        try visitor.visitSingularStringField(value: v, fieldNumber: 44)
      } }()
      try { if let v = _storage._rubyPackage {
        try visitor.visitSingularStringField(value: v, fieldNumber: 45)
      } }()
      try { if let v = _storage._features {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 50)
      } }()
      if !_storage._uninterpretedOption.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._uninterpretedOption, fieldNumber: 999)
      }
      try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FileOptions, rhs: Google_Protobuf_FileOptions) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._javaPackage != rhs_storage._javaPackage {return false}
        if _storage._javaOuterClassname != rhs_storage._javaOuterClassname {return false}
        if _storage._javaMultipleFiles != rhs_storage._javaMultipleFiles {return false}
        if _storage._javaGenerateEqualsAndHash != rhs_storage._javaGenerateEqualsAndHash {return false}
        if _storage._javaStringCheckUtf8 != rhs_storage._javaStringCheckUtf8 {return false}
        if _storage._optimizeFor != rhs_storage._optimizeFor {return false}
        if _storage._goPackage != rhs_storage._goPackage {return false}
        if _storage._ccGenericServices != rhs_storage._ccGenericServices {return false}
        if _storage._javaGenericServices != rhs_storage._javaGenericServices {return false}
        if _storage._pyGenericServices != rhs_storage._pyGenericServices {return false}
        if _storage._deprecated != rhs_storage._deprecated {return false}
        if _storage._ccEnableArenas != rhs_storage._ccEnableArenas {return false}
        if _storage._objcClassPrefix != rhs_storage._objcClassPrefix {return false}
        if _storage._csharpNamespace != rhs_storage._csharpNamespace {return false}
        if _storage._swiftPrefix != rhs_storage._swiftPrefix {return false}
        if _storage._phpClassPrefix != rhs_storage._phpClassPrefix {return false}
        if _storage._phpNamespace != rhs_storage._phpNamespace {return false}
        if _storage._phpMetadataNamespace != rhs_storage._phpMetadataNamespace {return false}
        if _storage._rubyPackage != rhs_storage._rubyPackage {return false}
        if _storage._features != rhs_storage._features {return false}
        if _storage._uninterpretedOption != rhs_storage._uninterpretedOption {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_FileOptions.OptimizeMode: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "SPEED"),
    2: .same(proto: "CODE_SIZE"),
    3: .same(proto: "LITE_RUNTIME"),
  ]
}

extension Google_Protobuf_MessageOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".MessageOptions"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: [],
      reservedRanges: [4..<7, 8..<10],
      numberNameMappings: [
        1: .standard(proto: "message_set_wire_format"),
        2: .standard(proto: "no_standard_descriptor_accessor"),
        3: .same(proto: "deprecated"),
        7: .standard(proto: "map_entry"),
        11: .standard(proto: "deprecated_legacy_json_field_conflicts"),
        12: .same(proto: "features"),
        999: .standard(proto: "uninterpreted_option"),
  ])

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if let v = self._features, !v.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self._messageSetWireFormat) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self._noStandardDescriptorAccessor) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self._deprecated) }()
      case 7: try { try decoder.decodeSingularBoolField(value: &self._mapEntry) }()
      case 11: try { try decoder.decodeSingularBoolField(value: &self._deprecatedLegacyJsonFieldConflicts) }()
      case 12: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_MessageOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._messageSetWireFormat {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._noStandardDescriptorAccessor {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._deprecated {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._mapEntry {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 7)
    } }()
    try { if let v = self._deprecatedLegacyJsonFieldConflicts {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 11)
    } }()
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 12)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_MessageOptions, rhs: Google_Protobuf_MessageOptions) -> Bool {
    if lhs._messageSetWireFormat != rhs._messageSetWireFormat {return false}
    if lhs._noStandardDescriptorAccessor != rhs._noStandardDescriptorAccessor {return false}
    if lhs._deprecated != rhs._deprecated {return false}
    if lhs._mapEntry != rhs._mapEntry {return false}
    if lhs._deprecatedLegacyJsonFieldConflicts != rhs._deprecatedLegacyJsonFieldConflicts {return false}
    if lhs._features != rhs._features {return false}
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_FieldOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FieldOptions"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: [],
      reservedRanges: [4..<5, 18..<19],
      numberNameMappings: [
        1: .same(proto: "ctype"),
        2: .same(proto: "packed"),
        6: .same(proto: "jstype"),
        5: .same(proto: "lazy"),
        15: .standard(proto: "unverified_lazy"),
        3: .same(proto: "deprecated"),
        10: .same(proto: "weak"),
        16: .standard(proto: "debug_redact"),
        17: .same(proto: "retention"),
        19: .same(proto: "targets"),
        20: .standard(proto: "edition_defaults"),
        21: .same(proto: "features"),
        22: .standard(proto: "feature_support"),
        999: .standard(proto: "uninterpreted_option"),
  ])

  fileprivate class _StorageClass {
    var _ctype: Google_Protobuf_FieldOptions.CType? = nil
    var _packed: Bool? = nil
    var _jstype: Google_Protobuf_FieldOptions.JSType? = nil
    var _lazy: Bool? = nil
    var _unverifiedLazy: Bool? = nil
    var _deprecated: Bool? = nil
    var _weak: Bool? = nil
    var _debugRedact: Bool? = nil
    var _retention: Google_Protobuf_FieldOptions.OptionRetention? = nil
    var _targets: [Google_Protobuf_FieldOptions.OptionTargetType] = []
    var _editionDefaults: [Google_Protobuf_FieldOptions.EditionDefault] = []
    var _features: Google_Protobuf_FeatureSet? = nil
    var _featureSupport: Google_Protobuf_FieldOptions.FeatureSupport? = nil
    var _uninterpretedOption: [Google_Protobuf_UninterpretedOption] = []

      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _ctype = source._ctype
      _packed = source._packed
      _jstype = source._jstype
      _lazy = source._lazy
      _unverifiedLazy = source._unverifiedLazy
      _deprecated = source._deprecated
      _weak = source._weak
      _debugRedact = source._debugRedact
      _retention = source._retention
      _targets = source._targets
      _editionDefaults = source._editionDefaults
      _features = source._features
      _featureSupport = source._featureSupport
      _uninterpretedOption = source._uninterpretedOption
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    return withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      if let v = _storage._features, !v.isInitialized {return false}
      if !Internal.areAllInitialized(_storage._uninterpretedOption) {return false}
      return true
    }
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 1: try { try decoder.decodeSingularEnumField(value: &_storage._ctype) }()
        case 2: try { try decoder.decodeSingularBoolField(value: &_storage._packed) }()
        case 3: try { try decoder.decodeSingularBoolField(value: &_storage._deprecated) }()
        case 5: try { try decoder.decodeSingularBoolField(value: &_storage._lazy) }()
        case 6: try { try decoder.decodeSingularEnumField(value: &_storage._jstype) }()
        case 10: try { try decoder.decodeSingularBoolField(value: &_storage._weak) }()
        case 15: try { try decoder.decodeSingularBoolField(value: &_storage._unverifiedLazy) }()
        case 16: try { try decoder.decodeSingularBoolField(value: &_storage._debugRedact) }()
        case 17: try { try decoder.decodeSingularEnumField(value: &_storage._retention) }()
        case 19: try { try decoder.decodeRepeatedEnumField(value: &_storage._targets) }()
        case 20: try { try decoder.decodeRepeatedMessageField(value: &_storage._editionDefaults) }()
        case 21: try { try decoder.decodeSingularMessageField(value: &_storage._features) }()
        case 22: try { try decoder.decodeSingularMessageField(value: &_storage._featureSupport) }()
        case 999: try { try decoder.decodeRepeatedMessageField(value: &_storage._uninterpretedOption) }()
        case 1000..<536870912:
          try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_FieldOptions.self, fieldNumber: fieldNumber) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._ctype {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 1)
      } }()
      try { if let v = _storage._packed {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 2)
      } }()
      try { if let v = _storage._deprecated {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 3)
      } }()
      try { if let v = _storage._lazy {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 5)
      } }()
      try { if let v = _storage._jstype {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 6)
      } }()
      try { if let v = _storage._weak {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 10)
      } }()
      try { if let v = _storage._unverifiedLazy {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 15)
      } }()
      try { if let v = _storage._debugRedact {
        try visitor.visitSingularBoolField(value: v, fieldNumber: 16)
      } }()
      try { if let v = _storage._retention {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 17)
      } }()
      if !_storage._targets.isEmpty {
        try visitor.visitRepeatedEnumField(value: _storage._targets, fieldNumber: 19)
      }
      if !_storage._editionDefaults.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._editionDefaults, fieldNumber: 20)
      }
      try { if let v = _storage._features {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 21)
      } }()
      try { if let v = _storage._featureSupport {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 22)
      } }()
      if !_storage._uninterpretedOption.isEmpty {
        try visitor.visitRepeatedMessageField(value: _storage._uninterpretedOption, fieldNumber: 999)
      }
      try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FieldOptions, rhs: Google_Protobuf_FieldOptions) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._ctype != rhs_storage._ctype {return false}
        if _storage._packed != rhs_storage._packed {return false}
        if _storage._jstype != rhs_storage._jstype {return false}
        if _storage._lazy != rhs_storage._lazy {return false}
        if _storage._unverifiedLazy != rhs_storage._unverifiedLazy {return false}
        if _storage._deprecated != rhs_storage._deprecated {return false}
        if _storage._weak != rhs_storage._weak {return false}
        if _storage._debugRedact != rhs_storage._debugRedact {return false}
        if _storage._retention != rhs_storage._retention {return false}
        if _storage._targets != rhs_storage._targets {return false}
        if _storage._editionDefaults != rhs_storage._editionDefaults {return false}
        if _storage._features != rhs_storage._features {return false}
        if _storage._featureSupport != rhs_storage._featureSupport {return false}
        if _storage._uninterpretedOption != rhs_storage._uninterpretedOption {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_FieldOptions.CType: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "STRING"),
    1: .same(proto: "CORD"),
    2: .same(proto: "STRING_PIECE"),
  ]
}

extension Google_Protobuf_FieldOptions.JSType: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "JS_NORMAL"),
    1: .same(proto: "JS_STRING"),
    2: .same(proto: "JS_NUMBER"),
  ]
}

extension Google_Protobuf_FieldOptions.OptionRetention: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "RETENTION_UNKNOWN"),
    1: .same(proto: "RETENTION_RUNTIME"),
    2: .same(proto: "RETENTION_SOURCE"),
  ]
}

extension Google_Protobuf_FieldOptions.OptionTargetType: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "TARGET_TYPE_UNKNOWN"),
    1: .same(proto: "TARGET_TYPE_FILE"),
    2: .same(proto: "TARGET_TYPE_EXTENSION_RANGE"),
    3: .same(proto: "TARGET_TYPE_MESSAGE"),
    4: .same(proto: "TARGET_TYPE_FIELD"),
    5: .same(proto: "TARGET_TYPE_ONEOF"),
    6: .same(proto: "TARGET_TYPE_ENUM"),
    7: .same(proto: "TARGET_TYPE_ENUM_ENTRY"),
    8: .same(proto: "TARGET_TYPE_SERVICE"),
    9: .same(proto: "TARGET_TYPE_METHOD"),
  ]
}

extension Google_Protobuf_FieldOptions.EditionDefault: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_FieldOptions.protoMessageName + ".EditionDefault"
  public static let _protobuf_nameMap: _NameMap = [
    3: .same(proto: "edition"),
    2: .same(proto: "value"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 2: try { try decoder.decodeSingularStringField(value: &self._value) }()
      case 3: try { try decoder.decodeSingularEnumField(value: &self._edition) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._value {
      try visitor.visitSingularStringField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._edition {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 3)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FieldOptions.EditionDefault, rhs: Google_Protobuf_FieldOptions.EditionDefault) -> Bool {
    if lhs._edition != rhs._edition {return false}
    if lhs._value != rhs._value {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FieldOptions.FeatureSupport: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_FieldOptions.protoMessageName + ".FeatureSupport"
  public static let _protobuf_nameMap: _NameMap = [
    1: .standard(proto: "edition_introduced"),
    2: .standard(proto: "edition_deprecated"),
    3: .standard(proto: "deprecation_warning"),
    4: .standard(proto: "edition_removed"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self._editionIntroduced) }()
      case 2: try { try decoder.decodeSingularEnumField(value: &self._editionDeprecated) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self._deprecationWarning) }()
      case 4: try { try decoder.decodeSingularEnumField(value: &self._editionRemoved) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._editionIntroduced {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._editionDeprecated {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._deprecationWarning {
      try visitor.visitSingularStringField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._editionRemoved {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 4)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FieldOptions.FeatureSupport, rhs: Google_Protobuf_FieldOptions.FeatureSupport) -> Bool {
    if lhs._editionIntroduced != rhs._editionIntroduced {return false}
    if lhs._editionDeprecated != rhs._editionDeprecated {return false}
    if lhs._deprecationWarning != rhs._deprecationWarning {return false}
    if lhs._editionRemoved != rhs._editionRemoved {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_OneofOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".OneofOptions"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "features"),
    999: .standard(proto: "uninterpreted_option"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if let v = self._features, !v.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_OneofOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 1)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_OneofOptions, rhs: Google_Protobuf_OneofOptions) -> Bool {
    if lhs._features != rhs._features {return false}
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_EnumOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".EnumOptions"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: [],
      reservedRanges: [5..<6],
      numberNameMappings: [
        2: .standard(proto: "allow_alias"),
        3: .same(proto: "deprecated"),
        6: .standard(proto: "deprecated_legacy_json_field_conflicts"),
        7: .same(proto: "features"),
        999: .standard(proto: "uninterpreted_option"),
  ])

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if let v = self._features, !v.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 2: try { try decoder.decodeSingularBoolField(value: &self._allowAlias) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self._deprecated) }()
      case 6: try { try decoder.decodeSingularBoolField(value: &self._deprecatedLegacyJsonFieldConflicts) }()
      case 7: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_EnumOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._allowAlias {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._deprecated {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._deprecatedLegacyJsonFieldConflicts {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 6)
    } }()
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 7)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_EnumOptions, rhs: Google_Protobuf_EnumOptions) -> Bool {
    if lhs._allowAlias != rhs._allowAlias {return false}
    if lhs._deprecated != rhs._deprecated {return false}
    if lhs._deprecatedLegacyJsonFieldConflicts != rhs._deprecatedLegacyJsonFieldConflicts {return false}
    if lhs._features != rhs._features {return false}
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_EnumValueOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".EnumValueOptions"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "deprecated"),
    2: .same(proto: "features"),
    3: .standard(proto: "debug_redact"),
    4: .standard(proto: "feature_support"),
    999: .standard(proto: "uninterpreted_option"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if let v = self._features, !v.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularBoolField(value: &self._deprecated) }()
      case 2: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 3: try { try decoder.decodeSingularBoolField(value: &self._debugRedact) }()
      case 4: try { try decoder.decodeSingularMessageField(value: &self._featureSupport) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_EnumValueOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._deprecated {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._debugRedact {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._featureSupport {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_EnumValueOptions, rhs: Google_Protobuf_EnumValueOptions) -> Bool {
    if lhs._deprecated != rhs._deprecated {return false}
    if lhs._features != rhs._features {return false}
    if lhs._debugRedact != rhs._debugRedact {return false}
    if lhs._featureSupport != rhs._featureSupport {return false}
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_ServiceOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".ServiceOptions"
  public static let _protobuf_nameMap: _NameMap = [
    34: .same(proto: "features"),
    33: .same(proto: "deprecated"),
    999: .standard(proto: "uninterpreted_option"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if let v = self._features, !v.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 33: try { try decoder.decodeSingularBoolField(value: &self._deprecated) }()
      case 34: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_ServiceOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._deprecated {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 33)
    } }()
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 34)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_ServiceOptions, rhs: Google_Protobuf_ServiceOptions) -> Bool {
    if lhs._features != rhs._features {return false}
    if lhs._deprecated != rhs._deprecated {return false}
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_MethodOptions: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".MethodOptions"
  public static let _protobuf_nameMap: _NameMap = [
    33: .same(proto: "deprecated"),
    34: .standard(proto: "idempotency_level"),
    35: .same(proto: "features"),
    999: .standard(proto: "uninterpreted_option"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    if let v = self._features, !v.isInitialized {return false}
    if !Internal.areAllInitialized(self.uninterpretedOption) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 33: try { try decoder.decodeSingularBoolField(value: &self._deprecated) }()
      case 34: try { try decoder.decodeSingularEnumField(value: &self._idempotencyLevel) }()
      case 35: try { try decoder.decodeSingularMessageField(value: &self._features) }()
      case 999: try { try decoder.decodeRepeatedMessageField(value: &self.uninterpretedOption) }()
      case 1000..<536870912:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_MethodOptions.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._deprecated {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 33)
    } }()
    try { if let v = self._idempotencyLevel {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 34)
    } }()
    try { if let v = self._features {
      try visitor.visitSingularMessageField(value: v, fieldNumber: 35)
    } }()
    if !self.uninterpretedOption.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.uninterpretedOption, fieldNumber: 999)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 536870912)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_MethodOptions, rhs: Google_Protobuf_MethodOptions) -> Bool {
    if lhs._deprecated != rhs._deprecated {return false}
    if lhs._idempotencyLevel != rhs._idempotencyLevel {return false}
    if lhs._features != rhs._features {return false}
    if lhs.uninterpretedOption != rhs.uninterpretedOption {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_MethodOptions.IdempotencyLevel: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "IDEMPOTENCY_UNKNOWN"),
    1: .same(proto: "NO_SIDE_EFFECTS"),
    2: .same(proto: "IDEMPOTENT"),
  ]
}

extension Google_Protobuf_UninterpretedOption: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".UninterpretedOption"
  public static let _protobuf_nameMap: _NameMap = [
    2: .same(proto: "name"),
    3: .standard(proto: "identifier_value"),
    4: .standard(proto: "positive_int_value"),
    5: .standard(proto: "negative_int_value"),
    6: .standard(proto: "double_value"),
    7: .standard(proto: "string_value"),
    8: .standard(proto: "aggregate_value"),
  ]

  public var isInitialized: Bool {
    if !Internal.areAllInitialized(self.name) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 2: try { try decoder.decodeRepeatedMessageField(value: &self.name) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self._identifierValue) }()
      case 4: try { try decoder.decodeSingularUInt64Field(value: &self._positiveIntValue) }()
      case 5: try { try decoder.decodeSingularInt64Field(value: &self._negativeIntValue) }()
      case 6: try { try decoder.decodeSingularDoubleField(value: &self._doubleValue) }()
      case 7: try { try decoder.decodeSingularBytesField(value: &self._stringValue) }()
      case 8: try { try decoder.decodeSingularStringField(value: &self._aggregateValue) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.name.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.name, fieldNumber: 2)
    }
    try { if let v = self._identifierValue {
      try visitor.visitSingularStringField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._positiveIntValue {
      try visitor.visitSingularUInt64Field(value: v, fieldNumber: 4)
    } }()
    try { if let v = self._negativeIntValue {
      try visitor.visitSingularInt64Field(value: v, fieldNumber: 5)
    } }()
    try { if let v = self._doubleValue {
      try visitor.visitSingularDoubleField(value: v, fieldNumber: 6)
    } }()
    try { if let v = self._stringValue {
      try visitor.visitSingularBytesField(value: v, fieldNumber: 7)
    } }()
    try { if let v = self._aggregateValue {
      try visitor.visitSingularStringField(value: v, fieldNumber: 8)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_UninterpretedOption, rhs: Google_Protobuf_UninterpretedOption) -> Bool {
    if lhs.name != rhs.name {return false}
    if lhs._identifierValue != rhs._identifierValue {return false}
    if lhs._positiveIntValue != rhs._positiveIntValue {return false}
    if lhs._negativeIntValue != rhs._negativeIntValue {return false}
    if lhs._doubleValue != rhs._doubleValue {return false}
    if lhs._stringValue != rhs._stringValue {return false}
    if lhs._aggregateValue != rhs._aggregateValue {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_UninterpretedOption.NamePart: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_UninterpretedOption.protoMessageName + ".NamePart"
  public static let _protobuf_nameMap: _NameMap = [
    1: .standard(proto: "name_part"),
    2: .standard(proto: "is_extension"),
  ]

  public var isInitialized: Bool {
    if self._namePart == nil {return false}
    if self._isExtension == nil {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularStringField(value: &self._namePart) }()
      case 2: try { try decoder.decodeSingularBoolField(value: &self._isExtension) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._namePart {
      try visitor.visitSingularStringField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._isExtension {
      try visitor.visitSingularBoolField(value: v, fieldNumber: 2)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_UninterpretedOption.NamePart, rhs: Google_Protobuf_UninterpretedOption.NamePart) -> Bool {
    if lhs._namePart != rhs._namePart {return false}
    if lhs._isExtension != rhs._isExtension {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FeatureSet: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FeatureSet"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: [],
      reservedRanges: [999..<1000],
      numberNameMappings: [
        1: .standard(proto: "field_presence"),
        2: .standard(proto: "enum_type"),
        3: .standard(proto: "repeated_field_encoding"),
        4: .standard(proto: "utf8_validation"),
        5: .standard(proto: "message_encoding"),
        6: .standard(proto: "json_format"),
        7: .standard(proto: "enforce_naming_style"),
        8: .standard(proto: "default_symbol_visibility"),
  ])

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeSingularEnumField(value: &self._fieldPresence) }()
      case 2: try { try decoder.decodeSingularEnumField(value: &self._enumType) }()
      case 3: try { try decoder.decodeSingularEnumField(value: &self._repeatedFieldEncoding) }()
      case 4: try { try decoder.decodeSingularEnumField(value: &self._utf8Validation) }()
      case 5: try { try decoder.decodeSingularEnumField(value: &self._messageEncoding) }()
      case 6: try { try decoder.decodeSingularEnumField(value: &self._jsonFormat) }()
      case 7: try { try decoder.decodeSingularEnumField(value: &self._enforceNamingStyle) }()
      case 8: try { try decoder.decodeSingularEnumField(value: &self._defaultSymbolVisibility) }()
      case 1000..<10001:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_FeatureSet.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    try { if let v = self._fieldPresence {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 1)
    } }()
    try { if let v = self._enumType {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._repeatedFieldEncoding {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._utf8Validation {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 4)
    } }()
    try { if let v = self._messageEncoding {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 5)
    } }()
    try { if let v = self._jsonFormat {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 6)
    } }()
    try { if let v = self._enforceNamingStyle {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 7)
    } }()
    try { if let v = self._defaultSymbolVisibility {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 8)
    } }()
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 1000, end: 10001)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FeatureSet, rhs: Google_Protobuf_FeatureSet) -> Bool {
    if lhs._fieldPresence != rhs._fieldPresence {return false}
    if lhs._enumType != rhs._enumType {return false}
    if lhs._repeatedFieldEncoding != rhs._repeatedFieldEncoding {return false}
    if lhs._utf8Validation != rhs._utf8Validation {return false}
    if lhs._messageEncoding != rhs._messageEncoding {return false}
    if lhs._jsonFormat != rhs._jsonFormat {return false}
    if lhs._enforceNamingStyle != rhs._enforceNamingStyle {return false}
    if lhs._defaultSymbolVisibility != rhs._defaultSymbolVisibility {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_FeatureSet.FieldPresence: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "FIELD_PRESENCE_UNKNOWN"),
    1: .same(proto: "EXPLICIT"),
    2: .same(proto: "IMPLICIT"),
    3: .same(proto: "LEGACY_REQUIRED"),
  ]
}

extension Google_Protobuf_FeatureSet.EnumType: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "ENUM_TYPE_UNKNOWN"),
    1: .same(proto: "OPEN"),
    2: .same(proto: "CLOSED"),
  ]
}

extension Google_Protobuf_FeatureSet.RepeatedFieldEncoding: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "REPEATED_FIELD_ENCODING_UNKNOWN"),
    1: .same(proto: "PACKED"),
    2: .same(proto: "EXPANDED"),
  ]
}

extension Google_Protobuf_FeatureSet.Utf8Validation: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "UTF8_VALIDATION_UNKNOWN"),
    2: .same(proto: "VERIFY"),
    3: .same(proto: "NONE"),
  ]
}

extension Google_Protobuf_FeatureSet.MessageEncoding: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "MESSAGE_ENCODING_UNKNOWN"),
    1: .same(proto: "LENGTH_PREFIXED"),
    2: .same(proto: "DELIMITED"),
  ]
}

extension Google_Protobuf_FeatureSet.JsonFormat: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "JSON_FORMAT_UNKNOWN"),
    1: .same(proto: "ALLOW"),
    2: .same(proto: "LEGACY_BEST_EFFORT"),
  ]
}

extension Google_Protobuf_FeatureSet.EnforceNamingStyle: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "ENFORCE_NAMING_STYLE_UNKNOWN"),
    1: .same(proto: "STYLE2024"),
    2: .same(proto: "STYLE_LEGACY"),
  ]
}

extension Google_Protobuf_FeatureSet.VisibilityFeature: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_FeatureSet.protoMessageName + ".VisibilityFeature"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: [],
      reservedRanges: [1..<536870912],
      numberNameMappings: [:])

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    // Load everything into unknown fields
    while try decoder.nextFieldNumber() != nil {}
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FeatureSet.VisibilityFeature, rhs: Google_Protobuf_FeatureSet.VisibilityFeature) -> Bool {
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FeatureSet.VisibilityFeature.DefaultSymbolVisibility: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "DEFAULT_SYMBOL_VISIBILITY_UNKNOWN"),
    1: .same(proto: "EXPORT_ALL"),
    2: .same(proto: "EXPORT_TOP_LEVEL"),
    3: .same(proto: "LOCAL_ALL"),
    4: .same(proto: "STRICT"),
  ]
}

extension Google_Protobuf_FeatureSetDefaults: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".FeatureSetDefaults"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "defaults"),
    4: .standard(proto: "minimum_edition"),
    5: .standard(proto: "maximum_edition"),
  ]

  public var isInitialized: Bool {
    if !Internal.areAllInitialized(self.defaults) {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.defaults) }()
      case 4: try { try decoder.decodeSingularEnumField(value: &self._minimumEdition) }()
      case 5: try { try decoder.decodeSingularEnumField(value: &self._maximumEdition) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.defaults.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.defaults, fieldNumber: 1)
    }
    try { if let v = self._minimumEdition {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 4)
    } }()
    try { if let v = self._maximumEdition {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 5)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FeatureSetDefaults, rhs: Google_Protobuf_FeatureSetDefaults) -> Bool {
    if lhs.defaults != rhs.defaults {return false}
    if lhs._minimumEdition != rhs._minimumEdition {return false}
    if lhs._maximumEdition != rhs._maximumEdition {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_FeatureSetDefaults.FeatureSetEditionDefault: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_FeatureSetDefaults.protoMessageName + ".FeatureSetEditionDefault"
  public static let _protobuf_nameMap = _NameMap(
      reservedNames: ["features"],
      reservedRanges: [1..<3],
      numberNameMappings: [
        3: .same(proto: "edition"),
        4: .standard(proto: "overridable_features"),
        5: .standard(proto: "fixed_features"),
  ])

  fileprivate class _StorageClass {
    var _edition: Google_Protobuf_Edition? = nil
    var _overridableFeatures: Google_Protobuf_FeatureSet? = nil
    var _fixedFeatures: Google_Protobuf_FeatureSet? = nil

      // This property is used as the initial default value for new instances of the type.
      // The type itself is protecting the reference to its storage via CoW semantics.
      // This will force a copy to be made of this reference when the first mutation occurs;
      // hence, it is safe to mark this as `nonisolated(unsafe)`.
      static nonisolated(unsafe) let defaultInstance = _StorageClass()

    private init() {}

    init(copying source: _StorageClass) {
      _edition = source._edition
      _overridableFeatures = source._overridableFeatures
      _fixedFeatures = source._fixedFeatures
    }
  }

  fileprivate mutating func _uniqueStorage() -> _StorageClass {
    if !isKnownUniquelyReferenced(&_storage) {
      _storage = _StorageClass(copying: _storage)
    }
    return _storage
  }

  public var isInitialized: Bool {
    return withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      if let v = _storage._overridableFeatures, !v.isInitialized {return false}
      if let v = _storage._fixedFeatures, !v.isInitialized {return false}
      return true
    }
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    _ = _uniqueStorage()
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      while let fieldNumber = try decoder.nextFieldNumber() {
        // The use of inline closures is to circumvent an issue where the compiler
        // allocates stack space for every case branch when no optimizations are
        // enabled. https://github.com/apple/swift-protobuf/issues/1034
        switch fieldNumber {
        case 3: try { try decoder.decodeSingularEnumField(value: &_storage._edition) }()
        case 4: try { try decoder.decodeSingularMessageField(value: &_storage._overridableFeatures) }()
        case 5: try { try decoder.decodeSingularMessageField(value: &_storage._fixedFeatures) }()
        default: break
        }
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    try withExtendedLifetime(_storage) { (_storage: _StorageClass) in
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every if/case branch local when no optimizations
      // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
      // https://github.com/apple/swift-protobuf/issues/1182
      try { if let v = _storage._edition {
        try visitor.visitSingularEnumField(value: v, fieldNumber: 3)
      } }()
      try { if let v = _storage._overridableFeatures {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 4)
      } }()
      try { if let v = _storage._fixedFeatures {
        try visitor.visitSingularMessageField(value: v, fieldNumber: 5)
      } }()
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_FeatureSetDefaults.FeatureSetEditionDefault, rhs: Google_Protobuf_FeatureSetDefaults.FeatureSetEditionDefault) -> Bool {
    if lhs._storage !== rhs._storage {
      let storagesAreEqual: Bool = withExtendedLifetime((lhs._storage, rhs._storage)) { (_args: (_StorageClass, _StorageClass)) in
        let _storage = _args.0
        let rhs_storage = _args.1
        if _storage._edition != rhs_storage._edition {return false}
        if _storage._overridableFeatures != rhs_storage._overridableFeatures {return false}
        if _storage._fixedFeatures != rhs_storage._fixedFeatures {return false}
        return true
      }
      if !storagesAreEqual {return false}
    }
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_SourceCodeInfo: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".SourceCodeInfo"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "location"),
  ]

  public var isInitialized: Bool {
    if !_protobuf_extensionFieldValues.isInitialized {return false}
    return true
  }

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.location) }()
      case 536000000:
        try { try decoder.decodeExtensionField(values: &_protobuf_extensionFieldValues, messageType: Google_Protobuf_SourceCodeInfo.self, fieldNumber: fieldNumber) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if !self.location.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.location, fieldNumber: 1)
    }
    try visitor.visitExtensionFields(fields: _protobuf_extensionFieldValues, start: 536000000, end: 536000001)
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_SourceCodeInfo, rhs: Google_Protobuf_SourceCodeInfo) -> Bool {
    if lhs.location != rhs.location {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    if lhs._protobuf_extensionFieldValues != rhs._protobuf_extensionFieldValues {return false}
    return true
  }
}

extension Google_Protobuf_SourceCodeInfo.Location: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_SourceCodeInfo.protoMessageName + ".Location"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "path"),
    2: .same(proto: "span"),
    3: .standard(proto: "leading_comments"),
    4: .standard(proto: "trailing_comments"),
    6: .standard(proto: "leading_detached_comments"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedInt32Field(value: &self.path) }()
      case 2: try { try decoder.decodeRepeatedInt32Field(value: &self.span) }()
      case 3: try { try decoder.decodeSingularStringField(value: &self._leadingComments) }()
      case 4: try { try decoder.decodeSingularStringField(value: &self._trailingComments) }()
      case 6: try { try decoder.decodeRepeatedStringField(value: &self.leadingDetachedComments) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.path.isEmpty {
      try visitor.visitPackedInt32Field(value: self.path, fieldNumber: 1)
    }
    if !self.span.isEmpty {
      try visitor.visitPackedInt32Field(value: self.span, fieldNumber: 2)
    }
    try { if let v = self._leadingComments {
      try visitor.visitSingularStringField(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._trailingComments {
      try visitor.visitSingularStringField(value: v, fieldNumber: 4)
    } }()
    if !self.leadingDetachedComments.isEmpty {
      try visitor.visitRepeatedStringField(value: self.leadingDetachedComments, fieldNumber: 6)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_SourceCodeInfo.Location, rhs: Google_Protobuf_SourceCodeInfo.Location) -> Bool {
    if lhs.path != rhs.path {return false}
    if lhs.span != rhs.span {return false}
    if lhs._leadingComments != rhs._leadingComments {return false}
    if lhs._trailingComments != rhs._trailingComments {return false}
    if lhs.leadingDetachedComments != rhs.leadingDetachedComments {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_GeneratedCodeInfo: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = _protobuf_package + ".GeneratedCodeInfo"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "annotation"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedMessageField(value: &self.annotation) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    if !self.annotation.isEmpty {
      try visitor.visitRepeatedMessageField(value: self.annotation, fieldNumber: 1)
    }
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_GeneratedCodeInfo, rhs: Google_Protobuf_GeneratedCodeInfo) -> Bool {
    if lhs.annotation != rhs.annotation {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_GeneratedCodeInfo.Annotation: Message, _MessageImplementationBase, _ProtoNameProviding {
  public static let protoMessageName: String = Google_Protobuf_GeneratedCodeInfo.protoMessageName + ".Annotation"
  public static let _protobuf_nameMap: _NameMap = [
    1: .same(proto: "path"),
    2: .standard(proto: "source_file"),
    3: .same(proto: "begin"),
    4: .same(proto: "end"),
    5: .same(proto: "semantic"),
  ]

  public mutating func decodeMessage<D: Decoder>(decoder: inout D) throws {
    while let fieldNumber = try decoder.nextFieldNumber() {
      // The use of inline closures is to circumvent an issue where the compiler
      // allocates stack space for every case branch when no optimizations are
      // enabled. https://github.com/apple/swift-protobuf/issues/1034
      switch fieldNumber {
      case 1: try { try decoder.decodeRepeatedInt32Field(value: &self.path) }()
      case 2: try { try decoder.decodeSingularStringField(value: &self._sourceFile) }()
      case 3: try { try decoder.decodeSingularInt32Field(value: &self._begin) }()
      case 4: try { try decoder.decodeSingularInt32Field(value: &self._end) }()
      case 5: try { try decoder.decodeSingularEnumField(value: &self._semantic) }()
      default: break
      }
    }
  }

  public func traverse<V: Visitor>(visitor: inout V) throws {
    // The use of inline closures is to circumvent an issue where the compiler
    // allocates stack space for every if/case branch local when no optimizations
    // are enabled. https://github.com/apple/swift-protobuf/issues/1034 and
    // https://github.com/apple/swift-protobuf/issues/1182
    if !self.path.isEmpty {
      try visitor.visitPackedInt32Field(value: self.path, fieldNumber: 1)
    }
    try { if let v = self._sourceFile {
      try visitor.visitSingularStringField(value: v, fieldNumber: 2)
    } }()
    try { if let v = self._begin {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 3)
    } }()
    try { if let v = self._end {
      try visitor.visitSingularInt32Field(value: v, fieldNumber: 4)
    } }()
    try { if let v = self._semantic {
      try visitor.visitSingularEnumField(value: v, fieldNumber: 5)
    } }()
    try unknownFields.traverse(visitor: &visitor)
  }

  public static func ==(lhs: Google_Protobuf_GeneratedCodeInfo.Annotation, rhs: Google_Protobuf_GeneratedCodeInfo.Annotation) -> Bool {
    if lhs.path != rhs.path {return false}
    if lhs._sourceFile != rhs._sourceFile {return false}
    if lhs._begin != rhs._begin {return false}
    if lhs._end != rhs._end {return false}
    if lhs._semantic != rhs._semantic {return false}
    if lhs.unknownFields != rhs.unknownFields {return false}
    return true
  }
}

extension Google_Protobuf_GeneratedCodeInfo.Annotation.Semantic: _ProtoNameProviding {
  public static let _protobuf_nameMap: _NameMap = [
    0: .same(proto: "NONE"),
    1: .same(proto: "SET"),
    2: .same(proto: "ALIAS"),
  ]
}
