// lib/pages/two_factor_settings_page.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../models/two_factor_auth.dart';
import '../service/two_factor_service.dart';
import '../components/two_factor_setup.dart';
import '../utils/totp_utils.dart';
import '../providers/app_provider.dart';
import '../flutter_flow/flutter_flow_theme.dart';
import '../flutter_flow/flutter_flow_widgets.dart';

class TwoFactorSettingsPage extends StatefulWidget {
  const TwoFactorSettingsPage({Key? key}) : super(key: key);

  @override
  State<TwoFactorSettingsPage> createState() => _TwoFactorSettingsPageState();
}

class _TwoFactorSettingsPageState extends State<TwoFactorSettingsPage> {
  final TwoFactorService _twoFactorService = TwoFactorService();

  TwoFactorStatus? _twoFactorStatus;
  bool _isLoading = false;
  String? _error;

  // For disable 2FA dialog
  bool _showDisableDialog = false;
  String _disableCode = '';
  bool _isDisabling = false;

  // For backup codes dialog
  bool _showBackupDialog = false;
  String _backupCode = '';
  bool _isGeneratingBackup = false;
  List<String>? _newBackupCodes;

  @override
  void initState() {
    super.initState();
    print('TwoFactorSettingsPage: initState called');
    _fetchTwoFactorStatus();
  }

  Future<void> _fetchTwoFactorStatus() async {
    print('TwoFactorSettingsPage: _fetchTwoFactorStatus called');
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final user = appProvider.authClient;

    if (user?.token == null) {
      print('TwoFactorSettingsPage: No user token available');
      return;
    }

    print('TwoFactorSettingsPage: User token available, fetching status');
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _twoFactorService.getTwoFactorStatus(user!.token);
      print(
          'TwoFactorSettingsPage: Response received - Success: ${response.success}');

      if (response.success && response.data != null) {
        print('TwoFactorSettingsPage: 2FA Status loaded successfully');
        setState(() {
          _twoFactorStatus = response.data;
          _isLoading = false;
        });
      } else {
        print('TwoFactorSettingsPage: Error in response - ${response.message}');
        setState(() {
          _error = response.message;
          _isLoading = false;
        });
      }
    } catch (error) {
      print('TwoFactorSettingsPage: Exception caught - $error');
      setState(() {
        _error = 'Failed to load 2FA status: $error';
        _isLoading = false;
      });
    }
  }

  Future<void> _disable2FA() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final user = appProvider.authClient;

    if (user?.token == null || _disableCode.length != 6) return;

    setState(() {
      _isDisabling = true;
    });

    try {
      final response =
          await _twoFactorService.disable2FA(user!.token, _disableCode);

      if (response.success) {
        setState(() {
          _showDisableDialog = false;
          _disableCode = '';
          _isDisabling = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('2FA has been disabled successfully'),
            backgroundColor: Colors.green,
          ),
        );

        _fetchTwoFactorStatus();
      } else {
        setState(() {
          _isDisabling = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      setState(() {
        _isDisabling = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to disable 2FA: $error'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _generateNewBackupCodes() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final user = appProvider.authClient;

    if (user?.token == null || _backupCode.length != 6) return;

    setState(() {
      _isGeneratingBackup = true;
    });

    try {
      final response = await _twoFactorService.generateNewBackupCodes(
        user!.token,
        _backupCode,
      );

      if (response.success && response.data != null) {
        setState(() {
          _newBackupCodes = response.data!.backupCodes;
          _backupCode = '';
          _isGeneratingBackup = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('New backup codes generated successfully'),
            backgroundColor: Colors.green,
          ),
        );

        _fetchTwoFactorStatus();
      } else {
        setState(() {
          _isGeneratingBackup = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (error) {
      setState(() {
        _isGeneratingBackup = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to generate backup codes: $error'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSetupDialog() {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    final user = appProvider.authClient;

    if (user?.token == null) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => TwoFactorSetupDialog(
        userToken: user!.token,
        onComplete: () {
          Navigator.of(context).pop();
          _fetchTwoFactorStatus();
        },
        onCancel: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
      appBar: AppBar(
        backgroundColor: FlutterFlowTheme.of(context).primaryColor,
        automaticallyImplyLeading: false,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_rounded,
            color: Colors.white,
            size: 30,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'Two-Factor Authentication',
          style: FlutterFlowTheme.of(context).title1.override(
                fontFamily: 'Outfit',
                color: Colors.white,
                fontSize: 22,
              ),
        ),
        centerTitle: false,
        elevation: 2,
      ),
      body: Stack(
        children: [
          SafeArea(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _error != null
                    ? _buildErrorState()
                    : _buildContent(),
          ),

          // Disable 2FA Dialog
          if (_showDisableDialog) _buildDisableDialog(),

          // Backup codes dialog
          if (_showBackupDialog) _buildBackupDialog(),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading 2FA Settings',
              style: FlutterFlowTheme.of(context).title2,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: FlutterFlowTheme.of(context).bodyText1,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FFButtonWidget(
              onPressed: _fetchTwoFactorStatus,
              text: 'Retry',
              options: FFButtonOptions(
                height: 40,
                color: FlutterFlowTheme.of(context).primaryColor,
                textStyle: FlutterFlowTheme.of(context).subtitle2.override(
                      fontFamily: 'Readex Pro',
                      color: Colors.white,
                    ),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main 2FA Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.security,
                      color: FlutterFlowTheme.of(context).primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Two-Factor Authentication',
                            style: FlutterFlowTheme.of(context).title2,
                          ),
                          Text(
                            'Add an extra layer of security to your account',
                            style:
                                FlutterFlowTheme.of(context).bodyText1.override(
                                      fontFamily: 'Readex Pro',
                                      color: Colors.grey.shade600,
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Status and toggle
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _twoFactorStatus?.twoFactorEnabled == true
                            ? 'Enabled'
                            : 'Disabled',
                        style: FlutterFlowTheme.of(context).title3.override(
                              fontFamily: 'Readex Pro',
                              color: _twoFactorStatus?.twoFactorEnabled == true
                                  ? Colors.green
                                  : Colors.grey.shade600,
                            ),
                      ),
                    ),
                    Switch(
                      value: _twoFactorStatus?.twoFactorEnabled ?? false,
                      onChanged: (value) {
                        if (value) {
                          _showSetupDialog();
                        } else {
                          setState(() => _showDisableDialog = true);
                        }
                      },
                      activeColor: FlutterFlowTheme.of(context).primaryColor,
                    ),
                  ],
                ),

                if (_twoFactorStatus?.twoFactorEnabled == true) ...[
                  const SizedBox(height: 24),

                  // Status info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '2FA is enabled since ${_twoFactorStatus!.twoFactorEnabledAt != null ? _formatDate(_twoFactorStatus!.twoFactorEnabledAt!) : 'unknown'}',
                            style:
                                FlutterFlowTheme.of(context).bodyText1.override(
                                      fontFamily: 'Readex Pro',
                                      color: Colors.green.shade700,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Backup codes section
                  Text(
                    'Backup Codes',
                    style: FlutterFlowTheme.of(context).title3,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'You have ${_twoFactorStatus!.unusedBackupCodes} unused backup codes remaining. These can be used to access your account if you lose your authenticator device.',
                    style: FlutterFlowTheme.of(context).bodyText1.override(
                          fontFamily: 'Readex Pro',
                          color: Colors.grey.shade600,
                        ),
                  ),
                  const SizedBox(height: 16),

                  FFButtonWidget(
                    onPressed: () => setState(() => _showBackupDialog = true),
                    text: 'Generate New Backup Codes',
                    icon: Icon(Icons.refresh, size: 18),
                    options: FFButtonOptions(
                      height: 40,
                      color: FlutterFlowTheme.of(context).secondaryBackground,
                      textStyle: FlutterFlowTheme.of(context)
                          .subtitle2
                          .override(
                            fontFamily: 'Readex Pro',
                            color: FlutterFlowTheme.of(context).primaryColor,
                          ),
                      borderSide: BorderSide(
                        color: FlutterFlowTheme.of(context).primaryColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ] else ...[
                  const SizedBox(height: 24),

                  // Warning message
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Your account is not protected by two-factor authentication. Enable 2FA to add an extra layer of security.',
                            style:
                                FlutterFlowTheme.of(context).bodyText1.override(
                                      fontFamily: 'Readex Pro',
                                      color: Colors.orange.shade700,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Info section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: FlutterFlowTheme.of(context).secondaryBackground,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'About Two-Factor Authentication',
                  style: FlutterFlowTheme.of(context).title3,
                ),
                const SizedBox(height: 16),
                _buildInfoItem(
                  Icons.smartphone,
                  'Authenticator App',
                  'Use Google Authenticator, Authy, or similar apps to generate verification codes.',
                ),
                const SizedBox(height: 16),
                _buildInfoItem(
                  Icons.backup,
                  'Backup Codes',
                  'Save your backup codes in a secure location for account recovery.',
                ),
                const SizedBox(height: 16),
                _buildInfoItem(
                  Icons.shield,
                  'Enhanced Security',
                  'Protect your account even if your password is compromised.',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: FlutterFlowTheme.of(context).primaryColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Readex Pro',
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: FlutterFlowTheme.of(context).bodyText1.override(
                      fontFamily: 'Readex Pro',
                      color: Colors.grey.shade600,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildDisableDialog() {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.orange,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Disable 2FA',
                        style: FlutterFlowTheme.of(context).title2,
                      ),
                    ),
                    IconButton(
                      onPressed: () =>
                          setState(() => _showDisableDialog = false),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Enter your current 6-digit verification code to disable two-factor authentication.',
                  style: FlutterFlowTheme.of(context).bodyText1,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                PinCodeTextField(
                  appContext: context,
                  length: 6,
                  onChanged: (value) => _disableCode = value,
                  onCompleted: (value) => _disableCode = value,
                  keyboardType: TextInputType.number,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(8),
                    fieldHeight: 50,
                    fieldWidth: 40,
                    activeFillColor: Colors.white,
                    inactiveFillColor: Colors.grey.shade100,
                    selectedFillColor: Colors.white,
                    activeColor: FlutterFlowTheme.of(context).primaryColor,
                    inactiveColor: Colors.grey.shade300,
                    selectedColor: FlutterFlowTheme.of(context).primaryColor,
                  ),
                  enableActiveFill: true,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: FFButtonWidget(
                        onPressed: () =>
                            setState(() => _showDisableDialog = false),
                        text: 'Cancel',
                        options: FFButtonOptions(
                          height: 40,
                          color:
                              FlutterFlowTheme.of(context).secondaryBackground,
                          textStyle: FlutterFlowTheme.of(context)
                              .subtitle2
                              .override(
                                fontFamily: 'Readex Pro',
                                color:
                                    FlutterFlowTheme.of(context).primaryColor,
                              ),
                          borderSide: BorderSide(
                            color: FlutterFlowTheme.of(context).primaryColor,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: FFButtonWidget(
                        onPressed: _isDisabling ? null : _disable2FA,
                        text: _isDisabling ? 'Disabling...' : 'Disable 2FA',
                        options: FFButtonOptions(
                          height: 40,
                          color: Colors.red,
                          textStyle:
                              FlutterFlowTheme.of(context).subtitle2.override(
                                    fontFamily: 'Readex Pro',
                                    color: Colors.white,
                                  ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackupDialog() {
    return Container(
      color: Colors.black.withOpacity(0.5),
      child: Center(
        child: Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.backup,
                      color: FlutterFlowTheme.of(context).primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Generate New Backup Codes',
                        style: FlutterFlowTheme.of(context).title2,
                      ),
                    ),
                    IconButton(
                      onPressed: () => setState(() {
                        _showBackupDialog = false;
                        _newBackupCodes = null;
                      }),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (_newBackupCodes == null) ...[
                  Text(
                    'Enter your current 6-digit verification code to generate new backup codes. This will invalidate all existing backup codes.',
                    style: FlutterFlowTheme.of(context).bodyText1,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  PinCodeTextField(
                    appContext: context,
                    length: 6,
                    onChanged: (value) => _backupCode = value,
                    onCompleted: (value) => _backupCode = value,
                    keyboardType: TextInputType.number,
                    pinTheme: PinTheme(
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(8),
                      fieldHeight: 50,
                      fieldWidth: 40,
                      activeFillColor: Colors.white,
                      inactiveFillColor: Colors.grey.shade100,
                      selectedFillColor: Colors.white,
                      activeColor: FlutterFlowTheme.of(context).primaryColor,
                      inactiveColor: Colors.grey.shade300,
                      selectedColor: FlutterFlowTheme.of(context).primaryColor,
                    ),
                    enableActiveFill: true,
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: FFButtonWidget(
                          onPressed: () =>
                              setState(() => _showBackupDialog = false),
                          text: 'Cancel',
                          options: FFButtonOptions(
                            height: 40,
                            color: FlutterFlowTheme.of(context)
                                .secondaryBackground,
                            textStyle: FlutterFlowTheme.of(context)
                                .subtitle2
                                .override(
                                  fontFamily: 'Readex Pro',
                                  color:
                                      FlutterFlowTheme.of(context).primaryColor,
                                ),
                            borderSide: BorderSide(
                              color: FlutterFlowTheme.of(context).primaryColor,
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: FFButtonWidget(
                          onPressed: _isGeneratingBackup
                              ? null
                              : _generateNewBackupCodes,
                          text: _isGeneratingBackup
                              ? 'Generating...'
                              : 'Generate',
                          options: FFButtonOptions(
                            height: 40,
                            color: FlutterFlowTheme.of(context).primaryColor,
                            textStyle:
                                FlutterFlowTheme.of(context).subtitle2.override(
                                      fontFamily: 'Readex Pro',
                                      color: Colors.white,
                                    ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // Show new backup codes
                  Text(
                    'New Backup Codes Generated',
                    style: FlutterFlowTheme.of(context).title3.override(
                          fontFamily: 'Readex Pro',
                          color: Colors.green,
                        ),
                  ),
                  const SizedBox(height: 16),

                  Text(
                    'Save these backup codes in a secure location. Each code can only be used once.',
                    style: FlutterFlowTheme.of(context).bodyText1,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Column(
                      children: [
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 3,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                          ),
                          itemCount: _newBackupCodes!.length,
                          itemBuilder: (context, index) {
                            return Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Center(
                                child: Text(
                                  TOTPUtils.formatBackupCode(
                                      _newBackupCodes![index]),
                                  style: const TextStyle(
                                    fontFamily: 'monospace',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 16),
                        FFButtonWidget(
                          onPressed: () =>
                              _copyToClipboard(_newBackupCodes!.join('\n')),
                          text: 'Copy All Codes',
                          icon: Icon(Icons.copy, size: 16),
                          options: FFButtonOptions(
                            height: 40,
                            color: FlutterFlowTheme.of(context).primaryColor,
                            textStyle:
                                FlutterFlowTheme.of(context).subtitle2.override(
                                      fontFamily: 'Readex Pro',
                                      color: Colors.white,
                                    ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  FFButtonWidget(
                    onPressed: () => setState(() {
                      _showBackupDialog = false;
                      _newBackupCodes = null;
                    }),
                    text: 'Done',
                    options: FFButtonOptions(
                      width: double.infinity,
                      height: 40,
                      color: Colors.green,
                      textStyle:
                          FlutterFlowTheme.of(context).subtitle2.override(
                                fontFamily: 'Readex Pro',
                                color: Colors.white,
                              ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
