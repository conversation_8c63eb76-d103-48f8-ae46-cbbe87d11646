# This is a generated file; do not edit or check into version control.
audioplayers=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.0/
audioplayers_android=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-5.2.1/
audioplayers_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/
audioplayers_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-4.2.1/
audioplayers_web=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-5.1.1/
audioplayers_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-4.2.1/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.9/
flutter_blue_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.35.5/
flutter_blue_plus_android=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_android-4.0.5/
flutter_blue_plus_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/
flutter_blue_plus_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_linux-3.0.2/
flutter_blue_plus_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_web-3.0.1/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
flutter_reactive_ble=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_reactive_ble-5.4.0/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/
flutter_sound=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/
flutter_sound_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.16.3/
geolocator=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-9.0.2/
geolocator_android=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/
geolocator_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
geolocator_web=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/
geolocator_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.1.3/
google_maps_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.3/
google_maps_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.2/
google_maps_flutter_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/
google_maps_flutter_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-0.8.9/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
mobile_scanner=/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
reactive_ble_mobile=/Users/<USER>/.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.0/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
