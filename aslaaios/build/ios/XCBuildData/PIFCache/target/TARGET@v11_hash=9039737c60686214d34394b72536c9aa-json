{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b690959e0c4f6182b00c24da5cfa11f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc0146af480e8f1ae2db2db9e556e58e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0763413af8ccaa9d98ede86abdc5d44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879e726a8f124b429f37df9cfca5f8110", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0763413af8ccaa9d98ede86abdc5d44", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/flutter_sound_core/flutter_sound_core-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_sound_core/flutter_sound_core.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "flutter_sound_core", "PRODUCT_NAME": "flutter_sound_core", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d783804f74ba32f77bf965e1bc147d51", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824d40d0a435154e6fd7d2cf89590d2db", "guid": "bfdfe7dc352907fc980b868725387e98b16e9bccc145372da1dfccfddeae3f6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8acb9a784b6f126937b674c23b3a4ef", "guid": "bfdfe7dc352907fc980b868725387e9889887a5bb179c3b03d62b66e5550c22e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4b97ce3862d8e8218d5e9a5078fd77", "guid": "bfdfe7dc352907fc980b868725387e98bd5202acabe7974a9a7dfbc64468474d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f939c2f4957126ef0e151c5092371e", "guid": "bfdfe7dc352907fc980b868725387e989bd8eed2752e69dcf0174015f336e722", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dbdab10d94d29ae2632398ddbd2c5f3", "guid": "bfdfe7dc352907fc980b868725387e9813ef333b110462ac75fe1e5da15b28e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f461441ea3b1f377ad322766801775ef", "guid": "bfdfe7dc352907fc980b868725387e989e72e03c6d1cc6b37ac0cd4394014b40", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9858d5da82b3a611512314a6f5743a02c3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989795b70951bdd04f1b19a90ed6a962a6", "guid": "bfdfe7dc352907fc980b868725387e981685b65ececc776858bad73329981bdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0fa4469ea2eb15b9f454ad0fb897b34", "guid": "bfdfe7dc352907fc980b868725387e989016d3b62e9d8c89cdf61edce1de86d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d854806e959eda74b3a15b5c4d8f17ed", "guid": "bfdfe7dc352907fc980b868725387e980530906fdb5ffd2d421b51fde19b9e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880890130c623752d2a03539a31b14776", "guid": "bfdfe7dc352907fc980b868725387e98d38e61772d300172ad791b2ee1f1746e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893fe7be1bee59ea21cde41c66df1b6db", "guid": "bfdfe7dc352907fc980b868725387e9846bb1be6ddb22b6365391017fb801589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809c91c1c5dace11d079323a2d1f6e946", "guid": "bfdfe7dc352907fc980b868725387e9817fbdd36e683eac6aa8e868428f2e8fc"}], "guid": "bfdfe7dc352907fc980b868725387e98d5dd745207efc7a57f0c59bb82d1e9bb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e1a8d1c5790182d4835bfc5119136e43", "guid": "bfdfe7dc352907fc980b868725387e98167e672ede22b07eb998641303d01672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98eaa7204025999857ff648e485ebc9c3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7340f73f00e5c96b91cb13a0391d8ea", "guid": "bfdfe7dc352907fc980b868725387e988f1986e9da52caba94d29790b08750f5"}], "guid": "bfdfe7dc352907fc980b868725387e98b455c6ce099da578a5b3272387f71cdf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981ddb32c43c3c496d31e09e765e75a954", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9817d41af66eee3a4c1e145e17163b22b8", "name": "flutter_sound_core", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ed846bc5edbcc85d935ace19b53742e0", "name": "flutter_sound_core.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}