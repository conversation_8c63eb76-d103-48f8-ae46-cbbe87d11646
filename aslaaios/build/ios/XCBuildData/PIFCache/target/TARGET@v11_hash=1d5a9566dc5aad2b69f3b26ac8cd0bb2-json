{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98027086a0a91cc749df31db074cfafe36", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879db014e28d34c72ca119bd6f44bbe02", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819d68cf8abfc37da4c4738d9448758f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c0c6c809e0bf3f386a3dc4dd0b3996b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819d68cf8abfc37da4c4738d9448758f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838c618f6d44bfe8379f59a2be62f5e51", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9862d3534eb10f345e0415e1b6e8f83251", "guid": "bfdfe7dc352907fc980b868725387e98cbc9011d23934bfb7c770d81acca0ad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983037b5d5dfd92581186e7c2219439555", "guid": "bfdfe7dc352907fc980b868725387e9871b8805a8f1ca09e388e4cca7535bc10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b526f22c4cf4c4bd6986a0d99c231030", "guid": "bfdfe7dc352907fc980b868725387e98cb3c7e8b68042da74d1c7c0a91cd8535"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8eb43c4a00bdba2d271244ace6d3608", "guid": "bfdfe7dc352907fc980b868725387e98a5fb2a5912f208e39b9acf64c9820214"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8dd6fe1a925c3f64c524a1d7efc5e78", "guid": "bfdfe7dc352907fc980b868725387e98aa9cc00f2ef3edf3a6f902dd4bf440d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f929929538f80f33d9348fa900048de5", "guid": "bfdfe7dc352907fc980b868725387e980725fdbed6628c0f1b690bb763f5cefb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a1bb2d86e0dc495dad9567ea325c52", "guid": "bfdfe7dc352907fc980b868725387e981e02cd6c7d87d3dd13fd8a6fc934a8ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c12fa7e707a1116546371e8052d2b8", "guid": "bfdfe7dc352907fc980b868725387e98a53a60a2e112575f7c41f18b8d1464dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987410ecf5e371a501b63cd308d6f454da", "guid": "bfdfe7dc352907fc980b868725387e984dd1e78925f221488b29bd4d5028e968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70017089e234cee622aa8ccd6c69ffc", "guid": "bfdfe7dc352907fc980b868725387e98af5be1c180b1a2194c2e5de2194a7518"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842cdffb768657780c29905f5a0170716", "guid": "bfdfe7dc352907fc980b868725387e98e24d3bd3f5f25574da82c5f40266d9a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d78944b5fb01a88663b2100edd1a3e1", "guid": "bfdfe7dc352907fc980b868725387e9883f4f9bb346e8762631964fcd7e751ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcb6288528acbda10f44e691a0c34d76", "guid": "bfdfe7dc352907fc980b868725387e9877bcd2494b13282e1efc40372cfed1ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22f1d36f4578c2d0ca4e922dabdd277", "guid": "bfdfe7dc352907fc980b868725387e98f41279b8e7f2ce1e9e6ac99c4e20b3d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d451f48c79600c463b3eb91b352a81fe", "guid": "bfdfe7dc352907fc980b868725387e984b142f1619d80ad62eb77511066f1df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af07accad84f11ffc0d91d2f01490201", "guid": "bfdfe7dc352907fc980b868725387e9869e9e6471386d541bdef42095883e6fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b4cd15b4a262276ea1325b271915f6e", "guid": "bfdfe7dc352907fc980b868725387e98951128230d3c5dbbc3ab85fe251eb77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733ac1b862b19045c6bc6bc2b3a7b0cb", "guid": "bfdfe7dc352907fc980b868725387e980cde2e17fe456ed00f3af21544b9533a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8df6f34852573c55915652c1ebfab36", "guid": "bfdfe7dc352907fc980b868725387e98ae7e0ae18c0d8c6dc36b33a0e3ba43ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1bd707eda9038672bbb521f54ce9725", "guid": "bfdfe7dc352907fc980b868725387e98db499deee867114c50b7e3da14d01e7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc25e7c89eee28508b3a5af41948239", "guid": "bfdfe7dc352907fc980b868725387e98c1fdf6204eafcc509d4726fa5cd75f9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88019211758474cd6b3c28bc08cf4e2", "guid": "bfdfe7dc352907fc980b868725387e9861165f80f8ac23a70b6e7e53103e27f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf179bc17059b747e270dd2b1834bd7d", "guid": "bfdfe7dc352907fc980b868725387e986218d35612cfa02551f4a19e0cb0c66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983339b75e48f098546722958fce8abe7d", "guid": "bfdfe7dc352907fc980b868725387e9832ca1446b0d425ba3c357119f073bd72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11d1c1cb03767f0c7ac0893a6ed91bf", "guid": "bfdfe7dc352907fc980b868725387e981178d3e0c2f8990ff30312395871d826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73cad4c989f28ca2f22727947597667", "guid": "bfdfe7dc352907fc980b868725387e98b3b439040958f58f061938d5ca4cc92f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a765c9f46feefd017ab31b25db06d1", "guid": "bfdfe7dc352907fc980b868725387e98d49f551d223704620195c4c9e31dd9d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983216e1777c37d52a352a8cb6cc608b3c", "guid": "bfdfe7dc352907fc980b868725387e9889e30c14d16bcfb8b2daf6c8a5794991"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4183ebf41f757048748fd4c7eb52e3b", "guid": "bfdfe7dc352907fc980b868725387e989f5d585abb2e6559aff9f79dd2bedbd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a349a668baca78ce47f752150a237029", "guid": "bfdfe7dc352907fc980b868725387e98fd86275fbf9c8deee0fe7b88e1c1f9ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820939d60e06f777de436bde912dd4549", "guid": "bfdfe7dc352907fc980b868725387e981ed04d02c87f6fc375a33ee4950c1a09"}], "guid": "bfdfe7dc352907fc980b868725387e98d2e5d1f9a41a804789819e39e0275b49", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b9c0903f7173cbb41b93ee9ba79b6435", "guid": "bfdfe7dc352907fc980b868725387e98ad81b0d6cfaf09feea532dc4a90b34b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834dc014147202559fffceeb996edfa3f", "guid": "bfdfe7dc352907fc980b868725387e9838b227e310371e0fa67ed8b826d45851"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad2ae90a8a45c7e03e49d42bd41cdfb8", "guid": "bfdfe7dc352907fc980b868725387e981230fbbdaff8721f5318a2cb665ca331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6d65362fa2cf0f0aec3f6332222b43", "guid": "bfdfe7dc352907fc980b868725387e98eca164f7da60600405ddac3eb4107059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988202df6026927e4d6021457112b61830", "guid": "bfdfe7dc352907fc980b868725387e98244b8a6cd12875fb723cd1dd74b4335a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6bd25fb661384436cb6dac23e75c529", "guid": "bfdfe7dc352907fc980b868725387e981ae88a26cfa82ed04d30f4d6d0d27e1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af430ede085d84f9bd1497ce1f8e1fb5", "guid": "bfdfe7dc352907fc980b868725387e984add43cf0f65d36373b38d70e01f320a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dc2042e28b43ca30f9f84e1d24dea0b", "guid": "bfdfe7dc352907fc980b868725387e98ba6bc1d8a12164adb99142879f0ce5a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98396ea6cdefe5cce701709dffc54be193", "guid": "bfdfe7dc352907fc980b868725387e98dcf04e46e1d347374e977832bee578e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980077f02a6f9c119795f5b0b1222f891a", "guid": "bfdfe7dc352907fc980b868725387e9890e36524f8659f64a61cbffc4d40d08b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c2cc2c540f93cd11bec5bd309301137", "guid": "bfdfe7dc352907fc980b868725387e984498680c93ed04b26560de01e855c987"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac4e55b51a598bd5747b9cb553e0788", "guid": "bfdfe7dc352907fc980b868725387e9872ab9def6a85b1dff524718b45caf337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98856c737a4e5349aad149fd6dd1cd206f", "guid": "bfdfe7dc352907fc980b868725387e98c18b165479cf77f5db92eb563b1e79f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b38fd110591a9662511a1bb2d8b62486", "guid": "bfdfe7dc352907fc980b868725387e989064280028ccda89716463c76c8203e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426f76d1bdbca6071cfe23e0c48a349d", "guid": "bfdfe7dc352907fc980b868725387e98270a2443371cdc22913b76706189afd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ce980bf64b3047ac12379e2d5f971e6", "guid": "bfdfe7dc352907fc980b868725387e98bfc2b353955a0c1a56016835f7a9bbc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a100ebd4e5ee506cd3b7b77481afeab3", "guid": "bfdfe7dc352907fc980b868725387e98a3259dc68ab54fd9713e1294d8b64d93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635f30b5cd0846923c972f55de5ac7df", "guid": "bfdfe7dc352907fc980b868725387e9883d443f19ed375ec955d9248668e9af0"}], "guid": "bfdfe7dc352907fc980b868725387e98d3ca8b19866d535f2a070d10c5499234", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98fa3ebcbe2184e76cecb6cb48e8f4fba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870350cda99093f910f2f9eecd3826fd9", "guid": "bfdfe7dc352907fc980b868725387e98e8e95a55b489f7f61dbb418d6d835058"}], "guid": "bfdfe7dc352907fc980b868725387e98ab84b6462545b58aa1620f8c0ed6079f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e56d08cdd6f381624e1dcac269a9fec9", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9827f6161f157c48252eb8b26d7a0cc139", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}