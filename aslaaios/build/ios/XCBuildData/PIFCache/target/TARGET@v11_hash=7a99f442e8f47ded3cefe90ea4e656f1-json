{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ad6af8525d3d7d6c95d8f703ec4b1a3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9843cd48fc5c4edb7e03a02fcacf0732d6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980aaca4e17296968ca6f553d8773d284b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989b3111d6f814da9c155c0bc8a00bcfef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980aaca4e17296968ca6f553d8773d284b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/reactive_ble_mobile/reactive_ble_mobile.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "reactive_ble_mobile", "PRODUCT_NAME": "reactive_ble_mobile", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985b852a54d6fb9b306b14a3ba5481099a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9891d2219d70ed40d8a7bc29a0ad3fbb0c", "guid": "bfdfe7dc352907fc980b868725387e983cc98b78a8486a76a730230fbee4aa4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b503e7eeac94473feb8651076c1d7e0", "guid": "bfdfe7dc352907fc980b868725387e98ec20d652cb45d4250569441be1456ce5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cb3e0e5de3db0ef9f7bd543a251d40ae", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bb89703687e4014c54fa9c60e47dfb5c", "guid": "bfdfe7dc352907fc980b868725387e983ef0387d6417242b44f0d8524e357c2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ec1853ad777581f32a17029b6fbffb", "guid": "bfdfe7dc352907fc980b868725387e980afb48aa973838e5054e9a463098639a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986df80bd26828d6a67f73930b7c252f7a", "guid": "bfdfe7dc352907fc980b868725387e9850b26f36612a866f7a3fb887a9c2c5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98854fa3e7c715ff3580dadd3988080b2d", "guid": "bfdfe7dc352907fc980b868725387e98dbcb195ee3786efcc597d19478eb8f6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bd121cc61a31f06a96af64074f3e898", "guid": "bfdfe7dc352907fc980b868725387e98a7eaa7bfed9eb016144c39946ab56f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f0e8d14542b8af59160593a5d5a6084", "guid": "bfdfe7dc352907fc980b868725387e9897d81eebee888ec413acc3fe75cd18af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e079bf2e007789e1457b690d8918b9fe", "guid": "bfdfe7dc352907fc980b868725387e9867a1830e820bb60868382cc5016831b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980121cf23a7c8de75cedf292f526bca7a", "guid": "bfdfe7dc352907fc980b868725387e9815d9a9fff5caaa47f523f2ec978dd62e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e3cfc600a752fa161a64a2a5002bc09", "guid": "bfdfe7dc352907fc980b868725387e987f275895674a76ae5f7b8a60dc1cadd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c58e00bb21c012916fd8e886c6f82c", "guid": "bfdfe7dc352907fc980b868725387e98b5b6be22a7130a08fc40279bc7ffd2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a874626e9d85618ff330a2e042b0379", "guid": "bfdfe7dc352907fc980b868725387e98df461b2a434f83acfb6d155fc3060507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb3a93111c5c6058e81b803a1007d4a2", "guid": "bfdfe7dc352907fc980b868725387e9881e19cb7b18d6a8af1a68a36e84cdc2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841b7f80308483a7b507224d41ca34cc4", "guid": "bfdfe7dc352907fc980b868725387e98d6da1299b66c6882da8855a2802d7ae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d8931d89dfa66573ad8aeadff658505", "guid": "bfdfe7dc352907fc980b868725387e983a72b83978a89493f3579cd1f82df7ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985546910704fbf4895749c88a96905a23", "guid": "bfdfe7dc352907fc980b868725387e987adec1d2775108056340ae1438bb6633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98009212781ceb0f717c779e1ac7f7145c", "guid": "bfdfe7dc352907fc980b868725387e98d2e78896cd30fc76e0bac8064ecd86b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0275923ee87584129bf0c2d1279bb39", "guid": "bfdfe7dc352907fc980b868725387e982eb45d4907d4884a53e8e3b811b19069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980373bfa0852dc74eca0a11f34b2928e7", "guid": "bfdfe7dc352907fc980b868725387e98ed96a287d9a9ff12a3e1c7de8987c039"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879fd88034d3e0a2871acd30dfe837ecf", "guid": "bfdfe7dc352907fc980b868725387e98d65c90e2b117336d6bfb7e04ba2f8560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988abb98e65a6ac5db78b4cf6afec5d9ac", "guid": "bfdfe7dc352907fc980b868725387e987471c817d6e450f12df8f41f442c6529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aa3bfe06f9bd44e70388c388938390b", "guid": "bfdfe7dc352907fc980b868725387e98995ff16c0607b14af00218d95769ef85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d177d16d07dd048913947244a9ac0f", "guid": "bfdfe7dc352907fc980b868725387e98f8a7992626d3eb9adb95e2d7525f34cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb2ab0fc58f4567f31fbff4a7d83e9ac", "guid": "bfdfe7dc352907fc980b868725387e984dfb7a5049d8be67b282e4be104d1094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d7650ff6d2682cfbbac808985094f2", "guid": "bfdfe7dc352907fc980b868725387e980046c0729984d0d85b8173f85104c080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c07d334cd2458e43f923aec212202806", "guid": "bfdfe7dc352907fc980b868725387e98f2a433f4ed3b59fd63e8d20214859c8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983557409ccb1edaa076b588e7792a1fbf", "guid": "bfdfe7dc352907fc980b868725387e98555ac170a3207e0ebb67ed89323f3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d0ca438af43e7e49945f26271b03ae6", "guid": "bfdfe7dc352907fc980b868725387e9878d64ffbc32a4c7a857f3f01173a406f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f344fb27c94c17fbd747611856a896d1", "guid": "bfdfe7dc352907fc980b868725387e9810b8dafca9c94868d2cb4d3cb182bd7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ee08eacd8c07615b75682762477d6a", "guid": "bfdfe7dc352907fc980b868725387e98a9ece4595116d036d3b0924bbd088d70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872aca880a774ad2ab4ee45029c26e9d3", "guid": "bfdfe7dc352907fc980b868725387e98876e0fddec8b7a158df3805c8f53a5ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1b1c2ccadef90b493c9ebb462e338d2", "guid": "bfdfe7dc352907fc980b868725387e98d89bc34f0bf11ba702311e4441528c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d0240a9a8269ad1c3e2085af8962d9b", "guid": "bfdfe7dc352907fc980b868725387e98534f86c80360550061c1615360a19c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f70dcaa848deddc082dc5f1c2a4a9f9", "guid": "bfdfe7dc352907fc980b868725387e98ce7f63b1028d7f975e0d8f62eb9cf8da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0f776ede72d5daca8c62804db6c94c", "guid": "bfdfe7dc352907fc980b868725387e98c1fffe8709f66c59f794363c6c280db2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f52c9437ea1b7fe75de5e6e363f20dc", "guid": "bfdfe7dc352907fc980b868725387e98b0dada825fc882ea46f84ad7ceeecde9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a4c90ef288e2ea9129c00d86433c0fe", "guid": "bfdfe7dc352907fc980b868725387e98bf7ae51413602f31402f6108bd88849a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f05da1c5313e1653d083520f8e482f", "guid": "bfdfe7dc352907fc980b868725387e98642d09c6f4d09cf2e8fccc08eaa5bdfc"}], "guid": "bfdfe7dc352907fc980b868725387e9818a214aa9a12eddf49b26a81fac412a4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98e80cad606cdb8562f34871f69a635336"}], "guid": "bfdfe7dc352907fc980b868725387e9888ccd7d6318a7aecc69bd1c683731da4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98364b0bc985bc2e5f2077344e04289a09", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98ea730784e531c7b29a4d3807c4f260df", "name": "Protobuf"}, {"guid": "bfdfe7dc352907fc980b868725387e9840fa72b1389229bc82a786c7bb54bc7b", "name": "SwiftProtobuf"}], "guid": "bfdfe7dc352907fc980b868725387e9882f8d386d4480ff95a26a30c940edf12", "name": "reactive_ble_mobile", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ad15dc253cf5cce54073e74a8e492f3f", "name": "reactive_ble_mobile.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}