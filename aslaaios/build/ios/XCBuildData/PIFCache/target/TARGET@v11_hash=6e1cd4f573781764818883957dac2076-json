{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845b6c9f2ed7334efbe7153e6fa189da0", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98813b2107f222c974f9d94408ff5a3352", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986073180e690f94e4bda9fa6ec25f4d20", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9893cc7599f9f6ff6838d7d95bda49b8b3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986073180e690f94e4bda9fa6ec25f4d20", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eca4d9348b99c172aae3c94b39173839", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8ac58af51e9296f254e4deac9a6332d", "guid": "bfdfe7dc352907fc980b868725387e98b6f6a54e426d38c10ae071f5068c7002", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7f40a72ea937aeaf20e83dac9ca39d1", "guid": "bfdfe7dc352907fc980b868725387e98992618a61ec67d7fe79be21720d53de7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986066359470d33b4c913375c506bd0586", "guid": "bfdfe7dc352907fc980b868725387e98c56c1bc7bf4aec6574e9883e08ac7189", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98996652529d4e35ef7382c631f1081693", "guid": "bfdfe7dc352907fc980b868725387e989bfc121d9f71c3756ff7839fbcc48a65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d100f3d94a6bee88c3a85d225c7e5fa9", "guid": "bfdfe7dc352907fc980b868725387e986ae3aac3322398b3e517785fe03029f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f34964863b8bc8c8790aa0d238490a", "guid": "bfdfe7dc352907fc980b868725387e982e2b26020659312e15d206fcd0a134f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26b82f6132757f7ac40473e0aa5ba5a", "guid": "bfdfe7dc352907fc980b868725387e98feb24c38bef75ae072d62a9a5e497441", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6880d7aeb857c1ea9046c308427600", "guid": "bfdfe7dc352907fc980b868725387e98cc9e09fec4c4c85b1e52792002629629", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b040af088d2e5292d5d104d6b8b2b18", "guid": "bfdfe7dc352907fc980b868725387e9860bc6c37ef821dac2c18172e7b787059", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a706dc752de14e9830e0802116bfb26c", "guid": "bfdfe7dc352907fc980b868725387e98d499b0d32ce8c5a6ab45f9cf30d966ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981747bf6277d31adbd1e81d1a2f948b3a", "guid": "bfdfe7dc352907fc980b868725387e98aeee7a39097dff4643c29c13dc48d3df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374fd14452808ed90ccbeb33bb66f984", "guid": "bfdfe7dc352907fc980b868725387e987c50a3f2a3bcf5565ca60cc91eefda0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c606755a20b57fb780d9ef3412464b9", "guid": "bfdfe7dc352907fc980b868725387e98605ede6aff42b4572e6d016a03a42da2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af88911d72dc2cecd0cdde47628af625", "guid": "bfdfe7dc352907fc980b868725387e98c5e4604712e8276cb60a915508d64894", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842e05bd23e86424a4076d29b215e1181", "guid": "bfdfe7dc352907fc980b868725387e98946cc1d03fac2c4195cb64ddc712aac2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c6f7f9d7a95fae921b2b3ca59394a1", "guid": "bfdfe7dc352907fc980b868725387e985540e84321ee0a825faed54c0f1142fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873ec53c290154e33d064f866b25b8cb0", "guid": "bfdfe7dc352907fc980b868725387e98ce3d992eeb26a0b06682ef25bac87c08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8429db79ac6a327e07a368a4a3a0f9d", "guid": "bfdfe7dc352907fc980b868725387e98f05e128db86ec68f965332f4d6abd79b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986012d73eb986e5a1aaa7952a9caeae59", "guid": "bfdfe7dc352907fc980b868725387e9891ada39230fc5bed2ceacce36e597fd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861a2f55ed8c6ada045fa8f0d5d31b712", "guid": "bfdfe7dc352907fc980b868725387e989626d55a5c0f5a801d34865f17e8ef5f", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b35f2140c1908280a190e037d164935", "guid": "bfdfe7dc352907fc980b868725387e982dd6a7b41831fb48628fa79b73100499", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a0bfafe21945a4d8dffe525e1cc2ac", "guid": "bfdfe7dc352907fc980b868725387e983737e4734cec408a851e13683eaa368f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b8fd3a66785ca4e8b9733c2d3fd98885", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984815821345749f68659171496e4cd9c6", "guid": "bfdfe7dc352907fc980b868725387e9856bfda9f61266e5d2028dc4047c4375b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98040ffa5573a6bd0cd5cfd4af8863193c", "guid": "bfdfe7dc352907fc980b868725387e9865a01c3cab54d05587247462b03061f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69eecd79d44fdd1a64239a96fdc84e8", "guid": "bfdfe7dc352907fc980b868725387e98f825193604b83474632baec22bb2ee75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5a87603428fae90f8afe9e4f56e0af3", "guid": "bfdfe7dc352907fc980b868725387e98cbec1bcb89bb45c800dd44d6664522d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875c38348923e37e3d157eb2803c4cf76", "guid": "bfdfe7dc352907fc980b868725387e9837e7f56d40747da4cd4218e45b357768"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3f34fe3108ed4f00260c92455f57b6", "guid": "bfdfe7dc352907fc980b868725387e98a7d06c0de56a510c8b7cb05bfd1d8a9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b03c3f0959d335e519fc792467a83ae4", "guid": "bfdfe7dc352907fc980b868725387e98af30bcd14721e961d742f460aeb46b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d832cda6af51aa1e2092abcf13f27f7", "guid": "bfdfe7dc352907fc980b868725387e98c468211428fd61c207b7ddd2878d5df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b94edb4af824870b15c4061e76e4a07", "guid": "bfdfe7dc352907fc980b868725387e9885409cb60940e2928c422692b1e808f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886b1f61052055847698fefbef8f19993", "guid": "bfdfe7dc352907fc980b868725387e986bd19217cae5a52f173683b9238c9f69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8daaabda770f0cc7e817bd386febd90", "guid": "bfdfe7dc352907fc980b868725387e98fc830511478aaee23c14bf093c793e9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4cc7dfb434bf5720b7bccddfeec6514", "guid": "bfdfe7dc352907fc980b868725387e984539568f111dfaa1470a6990017fc9d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae73c38083ef406b68b6cdd426369f96", "guid": "bfdfe7dc352907fc980b868725387e980a0b08ffd4ec2c27fff176ce79935813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c3aefae2b9a4417440f67639695df6", "guid": "bfdfe7dc352907fc980b868725387e987b470275cec69c40ceccc59212d06eaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d034dee23876b09451b4f6457f5499a", "guid": "bfdfe7dc352907fc980b868725387e98fe6cfaf1aeda15938e7ab97405d28e3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98161b2d9b33aeddccde02b6279859ace1", "guid": "bfdfe7dc352907fc980b868725387e9866910443bf67bacc100eb2905b4887ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b997faebb37a3dbfec73149252b06f1", "guid": "bfdfe7dc352907fc980b868725387e98f35a211776cc592c7f2d9ad46922604c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827969122ecb121246400284b77aac2b2", "guid": "bfdfe7dc352907fc980b868725387e98a43b7813f993e97c72f46f8ac6233803"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987117a696df478216ae92c82940acb1f2", "guid": "bfdfe7dc352907fc980b868725387e98739db8398fdd6a25edf25432c33edd28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a22e2851a0f20411ac6fa32ecfe3365", "guid": "bfdfe7dc352907fc980b868725387e983f5e15cf9da2dd9db90ca6db6a85de0f"}], "guid": "bfdfe7dc352907fc980b868725387e98b8499929ad314192cc173b8a74241db7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e9826ac24ac822b0e9c57c76b94c9c324c2"}], "guid": "bfdfe7dc352907fc980b868725387e98e86d5f183649ea3657433e1dc6843dd6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9813f88955c848856258ae086e337a263a", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98228dca8401b56aa25eb852751d048b86", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}