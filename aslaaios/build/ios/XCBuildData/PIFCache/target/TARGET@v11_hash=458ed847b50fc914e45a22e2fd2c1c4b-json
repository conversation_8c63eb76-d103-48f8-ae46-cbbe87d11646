{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f37b200e27684936ac3d611d52f3832", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f00f43339ab381954a5de7eec39c289", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f00f43339ab381954a5de7eec39c289", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fae22b262873f034024e1aba173aa82d", "guid": "bfdfe7dc352907fc980b868725387e98fbc702eaedd4bfca78d105f13a70b613", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea7fee445ab8fb9e2a43b0e92841e44c", "guid": "bfdfe7dc352907fc980b868725387e983884dc90b8b42e01ec50a0d846553c2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982320f31435220dd401442af66f2eb582", "guid": "bfdfe7dc352907fc980b868725387e98d354b462cae50da99b99aa4402d77635", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0be361b76c232490504959b467c35f1", "guid": "bfdfe7dc352907fc980b868725387e986302c7a6a06f94fbe5a4cfd108a195f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b3f596610cfb81d22c5f5494dbd1f1", "guid": "bfdfe7dc352907fc980b868725387e98e04e2ae0d24cf19846cbe457a19a47dc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0d13a5dab1202f6febba0b3f3a2ca6", "guid": "bfdfe7dc352907fc980b868725387e98fc1cbd9d1acf98d9e6cf0f7ac6a90ec3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e62176c011c97a8387baf83047c00668", "guid": "bfdfe7dc352907fc980b868725387e9818d3896ae10cd11e3d30dde99a68588c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e539d371e2faba20cb1363962177e5f", "guid": "bfdfe7dc352907fc980b868725387e98da4e0ccbb9c24cefbc1f917f7256378b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859c93424e90d2e5d6025e5a464548950", "guid": "bfdfe7dc352907fc980b868725387e98230602d2b1b510f17056c29f8bb25252", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c9cf69b3f23ab4efaa735c9c0a0aba1", "guid": "bfdfe7dc352907fc980b868725387e98ce95f536f5ac3dd5f897454d0cd7096c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca4eb2a55a6de01994a6091e8e967c21", "guid": "bfdfe7dc352907fc980b868725387e987b3a26dd6616c5cd3ae6ea10c7bcbc3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873f7d0372ff359dffa8d29c5da538115", "guid": "bfdfe7dc352907fc980b868725387e98d4c5338a73d9357c218940ca4852b06a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808356a95a65b12346fc9e1ef0ea9c5c7", "guid": "bfdfe7dc352907fc980b868725387e9824f46e12e60cfe325e903e3ddca5bbe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98765d41fcce3103d9d7a287d466cb7954", "guid": "bfdfe7dc352907fc980b868725387e9829403ac69d0816ade1c63c694147060b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981407e846304dc6b6e63348a64aab4f6d", "guid": "bfdfe7dc352907fc980b868725387e98a5bf4ac606b8c39f0bf85024c5937632", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846767538465b1eb3ab923d9b69e92676", "guid": "bfdfe7dc352907fc980b868725387e98c36f1e9f2091843073f3d8285326a3eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc739c2d094ea72fa6c542fc0f51e795", "guid": "bfdfe7dc352907fc980b868725387e98031750c50923a6c5ee5de744c15f9890", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2821bbeae2f38fbd6d4e43909c5ea18", "guid": "bfdfe7dc352907fc980b868725387e9865cd0fcecbc83e19a31353d6cad90455", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cfa1ece350ec873fe57c083a7166c93", "guid": "bfdfe7dc352907fc980b868725387e98eabcfbd0cbcfde61b447fc4f94c4fee7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b5b25018bf70e65ad8464b11d32ac0", "guid": "bfdfe7dc352907fc980b868725387e986edb580ea54331aad54b18c9a44a64f4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf60ebc5c910d1ff29a548e35af607d8", "guid": "bfdfe7dc352907fc980b868725387e989e29793aa12327ca70cbec80827193b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871451d98f11659b0a0c78ea7af2afc5b", "guid": "bfdfe7dc352907fc980b868725387e98f7860c3371f1b12ea8fd4361d731257c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf105a44950e9d1d6340c4710e117ff", "guid": "bfdfe7dc352907fc980b868725387e9850f2ab391d38c72735bd9776bbfa7a4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d6512072d17709cee347c0dc1a9b980", "guid": "bfdfe7dc352907fc980b868725387e98cfe7cf654d1e23f160449e40a7806de5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0e06281f5132dfc66f668a3fc4fd89e", "guid": "bfdfe7dc352907fc980b868725387e985fff404a7b9b4972d09ab0f20b7c7a2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989182f136efb2b50b054a97a5d5504435", "guid": "bfdfe7dc352907fc980b868725387e98f4686d90b002955dbb0ce315ff53ab65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbeb84a1c0a8138b41f332b9dc9e5487", "guid": "bfdfe7dc352907fc980b868725387e9808c176f16d27259d5b48402f2ed27d71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa7bd01456a2007b7ae01decf0cc4713", "guid": "bfdfe7dc352907fc980b868725387e983ac8fff2009595ab2efd438698a525b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98852c8341b2fee160e8ac060e991d7b90", "guid": "bfdfe7dc352907fc980b868725387e9810f77ed7c57412caad24cfc87232a02c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb90945c2df0bed39f1642f9cb67262", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7e029be6e9d6356316eba19bf3348e", "guid": "bfdfe7dc352907fc980b868725387e980ca5e66c2de3486d99bb194423df915a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985302ba9c06d5032e88befdde68b89415", "guid": "bfdfe7dc352907fc980b868725387e98a51a8f4d5a3211186091e4ec4e9bb2a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826dfc914d91282e902747a1b5ed23215", "guid": "bfdfe7dc352907fc980b868725387e98b5c95a6073f2030a3fa7d1e47f2fbd18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dce0d66ce7248f066a29de4a1be9c229", "guid": "bfdfe7dc352907fc980b868725387e98f0587ce39ef8faded9c98fcae28c4c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98086c6bbcf2cb41abcd68717302db1d3a", "guid": "bfdfe7dc352907fc980b868725387e98708284fd653c418be4a107595f1464c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3e329ae984972d6f9edf1578a2d1ed5", "guid": "bfdfe7dc352907fc980b868725387e98d4c71d47c35817cca9bec9d6757afc2a"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}