{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e403749f8a4063427b21fa08fc6de42", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee69e68de26ecd578aa9f360c8e26be6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee69e68de26ecd578aa9f360c8e26be6", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/somecode/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98201800c3837f2fc897020517d22b9113", "guid": "bfdfe7dc352907fc980b868725387e982ef32830090a5f180689e16980928fd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb44a156da403d38fc11fb53da5f40e3", "guid": "bfdfe7dc352907fc980b868725387e985a916ced4ab8bf3396d2522efbf655a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98808b09fd05ad5dee67e65a2f67823b66", "guid": "bfdfe7dc352907fc980b868725387e98ff53f399fa1e2ecb7849daf4286e9ade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4bf08dd302040189310bec2c5ef6d00", "guid": "bfdfe7dc352907fc980b868725387e988ad8e0322ec48311f0d9e5a75f54c724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd1a0a9c43e3a5924abefc21229f33f3", "guid": "bfdfe7dc352907fc980b868725387e98b11ed937bba18c7408a49a9a028bb33f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d7f7045e2dfe0cd1cd284a86e34a53", "guid": "bfdfe7dc352907fc980b868725387e983d80b84cb41fbc5fdcc8bc3d451f292d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28e61ba78fbd18cff12c34d7678858f", "guid": "bfdfe7dc352907fc980b868725387e983bf5db94459714fd2562bfa5ba3794e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872bfcb23ed6396e4927b0e159ccb4513", "guid": "bfdfe7dc352907fc980b868725387e98b4cf2c1398499b177aa5f12416a2cefc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad5675f864c83d218c3af7e97ed123a", "guid": "bfdfe7dc352907fc980b868725387e98a5931e111640051bba64bb234ef28a63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883f67d4e87d37552d981298ac7dc74c", "guid": "bfdfe7dc352907fc980b868725387e984e2d3cd6dd0a8b8ce5a9345bf5eed7de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cac152bde27fc060205c2a77ca574bad", "guid": "bfdfe7dc352907fc980b868725387e984bcefc59b1341fdd0e8bad71dba39c92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d1b88b4e44205d8ea8c5bf127fe5e5", "guid": "bfdfe7dc352907fc980b868725387e98c7eb3ea66f1fa44a1f5858283c92137d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d1ef0da5655bb9a464cd44034ddde4", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e246be791f443d4c228053c37073f58", "guid": "bfdfe7dc352907fc980b868725387e98cdc5844f930b9d0fd5308dd5856a500e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d75d957db5e2ef366f270fb1d722417", "guid": "bfdfe7dc352907fc980b868725387e988f5bb97be79cc2d8abff7c92bd4b7567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e120ebb338f93d31931a8797e5b750be", "guid": "bfdfe7dc352907fc980b868725387e983de4f1e8f5a97a6028a6bb34227f49d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a17658aadd029959e216c92283aacaa0", "guid": "bfdfe7dc352907fc980b868725387e98f2f63511bf606bb48ce3eb1b71b306f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cb1dfab422b96426985a7d4fd87a1bc", "guid": "bfdfe7dc352907fc980b868725387e98327b38ee62f98da7d09431ea5004a053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb564201259a143a1d34e61c057b776d", "guid": "bfdfe7dc352907fc980b868725387e980188baa19b7914aff2e34c8da54773e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce73c92bea11de9c8156adf27c1198b5", "guid": "bfdfe7dc352907fc980b868725387e9812d25da0977d54b4f93c43814429ebf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98264cbd1f7449a6bc43200153c0d708b7", "guid": "bfdfe7dc352907fc980b868725387e98b9ff284581573c4ba830aef19b6cb271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abffc374e4adb3be65be8e4d56f7cde7", "guid": "bfdfe7dc352907fc980b868725387e98938189fe00ada6ed049fb839ef6bf7d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf017f9b3c42060a474833dac002f950", "guid": "bfdfe7dc352907fc980b868725387e9864333a3fadcb2636b3f7fea467c141f6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e423c0a3b664322b841df99518563c16", "guid": "bfdfe7dc352907fc980b868725387e982a61503d8185fa65d0d61935791efb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceebff328b9d31d3c5649189d2a0c206", "guid": "bfdfe7dc352907fc980b868725387e9835220589345efb92af42631dbf6b9459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852e837fd83f5f4ddac03ba92b311fa7d", "guid": "bfdfe7dc352907fc980b868725387e98c4ed4b65682f6200b53ab06f33e7c896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98683470657f9fd4a139838957316271a8", "guid": "bfdfe7dc352907fc980b868725387e98f67d51cdaaf5e1f78be421f374efde62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622b3a5ab9939fb5514ee037340e0d92", "guid": "bfdfe7dc352907fc980b868725387e98aa97ddbe032d174d7e0e5ddbc64b9d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98855b2ad3355ddbd3ae5686991262f2a0", "guid": "bfdfe7dc352907fc980b868725387e98ff8928328841298b0ed62804c598770b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845e015e00c81ae43f9650473aa7f9cad", "guid": "bfdfe7dc352907fc980b868725387e98ef84c0ffd43efae0c864668786839def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b15bfac95d064b3b528952169801b4e3", "guid": "bfdfe7dc352907fc980b868725387e9848cadd1ba7bb24a85b533aabf8e22799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1af7fb966cb4f52895252ebf90cb4b", "guid": "bfdfe7dc352907fc980b868725387e9821497750e138db752421f5224d128654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c883d691a3bf91fcc6ba55317377bae4", "guid": "bfdfe7dc352907fc980b868725387e984a4a9db5329a7711e351ab57794c53a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98148522d9d11c208304fe52ac7d3a2e97", "guid": "bfdfe7dc352907fc980b868725387e9833885db617a4d69d1571730987fbd3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e1eceb4f9e5e890621d363487d285b3", "guid": "bfdfe7dc352907fc980b868725387e9824cbbfe0ddc5f68e6717f61ca670184f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853bb14169adfa944a683119014b1867b", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f9f3056e4d57975fd833f22daac880e", "guid": "bfdfe7dc352907fc980b868725387e984e5371b8cd4eca1a3ea6dfcf7d307c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac636ca890b3b3231d93b90ffbac9d2d", "guid": "bfdfe7dc352907fc980b868725387e98d3572e24141325a30bc3c0a31c41fa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed637f5de97888f703e3c17e14cfb109", "guid": "bfdfe7dc352907fc980b868725387e98b2f53712d1d0af9bc7d13919388a2c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9af6964f9f780690c73e217a954b0a", "guid": "bfdfe7dc352907fc980b868725387e98e29bf2527a03f8a7f0ee4f8fb682157b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98680cf5157347b7814a2eeffde7a0c60e", "guid": "bfdfe7dc352907fc980b868725387e9827d1f3d71d300da27dcd08b0f8c4d523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e99677efcc70e620c1bdb59c27c71f4b", "guid": "bfdfe7dc352907fc980b868725387e98eee17b6918192a58e3242e8202b1af3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98868e519c23328f2c8a8177536c2509e1", "guid": "bfdfe7dc352907fc980b868725387e98319f0ee3ee4a0d94236a327f7214191b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5b525acb545eb44cbd9d3a08730e02", "guid": "bfdfe7dc352907fc980b868725387e9889d6808f96db31c0fa88901646d50905"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}