{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987e91c425d83be4e5e1c4500537ceeb2a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d5f112e4cc8b176f12cf178860e2d0f1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831b14aa04aa3a142c876afa223adf451", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987900f4103ea939c9fee8623d6604e878", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9831b14aa04aa3a142c876afa223adf451", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98213a19765310733755f4cae7aa47b4d6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ddbff2fef2834f8070a9159f6fea9f92", "guid": "bfdfe7dc352907fc980b868725387e983bdf78225ee3be395a9b4f73e1f74320"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98770a0339b5a17426fb7af9493ca8698d", "guid": "bfdfe7dc352907fc980b868725387e9812ac23e68aaee7881efdb2dd636efbe5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f0c6ea79c9bca8c4cac5318e9469487", "guid": "bfdfe7dc352907fc980b868725387e987cc57ef65818a7ff6a7608e06a377be6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98452ca98c60c1ba812eb98617d7edb953", "guid": "bfdfe7dc352907fc980b868725387e9838fee39f2738933e24d0df5e6026821f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819780539fe3502c422abd24e4fbbc93a", "guid": "bfdfe7dc352907fc980b868725387e9830d6984738b8a987b1892fd9b5ba3c23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c827e374ff0c7662270a34fd75a8c523", "guid": "bfdfe7dc352907fc980b868725387e9805f4a54956d01569c4264fd465d53cee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985223d4e2c0ae0ed18329488325089e25", "guid": "bfdfe7dc352907fc980b868725387e9885d4df77013713e1abbcdf8739ebecdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b91017059480845cea251fa72e4a555", "guid": "bfdfe7dc352907fc980b868725387e98d560c692c9bd27bc6df677d82313c7f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899cd0378fde5fd94a80d33fe7f6b6441", "guid": "bfdfe7dc352907fc980b868725387e984d6f505330770002971a98970fb5437a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98802b7e340fd1019a350c4dd2a3f2af7f", "guid": "bfdfe7dc352907fc980b868725387e98fcf68c80d2a7805da4041aae4ca52625"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a618d6753db75c9a745a9e297f8242b", "guid": "bfdfe7dc352907fc980b868725387e98de826437a4eab234c78762b020f7b13d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff05d231fe7b3c93737d59298bdabec", "guid": "bfdfe7dc352907fc980b868725387e98266802be31c03d467fc83ca3671cb59b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98111aad78c3927b211214a05609ca8c5a", "guid": "bfdfe7dc352907fc980b868725387e985cb0335c6d6213c8d35418d87a99394e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e54d54079310f31abb07efbe9b8841", "guid": "bfdfe7dc352907fc980b868725387e98fd6859bc0405c7985235160a103f290f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7dfe792046a0c2c053922f5e9f5b4c9", "guid": "bfdfe7dc352907fc980b868725387e984782dc3efacc426e59cc2053331399de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da48ab4731506da281d899ec7c69f80", "guid": "bfdfe7dc352907fc980b868725387e982a4f0e4b08b9954348f79112ca4f48db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e0625997110cd12fed3ba921a54cdd", "guid": "bfdfe7dc352907fc980b868725387e980e7ad5324f9870dcfea0506509f8405a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980225bbe449672c4a2d1bf4c421fd45ba", "guid": "bfdfe7dc352907fc980b868725387e989b792d2f0a5dd6e9eb7419efe2ee2fc3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868960bf48a81dc0a6b2a391baa945707", "guid": "bfdfe7dc352907fc980b868725387e985f159895c6d6745dadff05d47fc084d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830a09b579e790c0082b355605a3cd00", "guid": "bfdfe7dc352907fc980b868725387e98e017d52f5e1e725117000d6dd3ed800a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f024a5fa20206ad1b67b27f3ba6b7695", "guid": "bfdfe7dc352907fc980b868725387e987d3a6f5db37a4a4b7d2ef67a7f15d70f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cb194aee62e1b9a981be42aea04651e", "guid": "bfdfe7dc352907fc980b868725387e98056fe65009256c1e4836c5e597a53ebe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810d1da66416368daa53f6fd9644a757f", "guid": "bfdfe7dc352907fc980b868725387e98aba188e0a31ef434bd67771bed275a9b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9845408bedcff2ba38d11132594c023dcf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988be8a4a93d48d1e0636c5cd3ea5dd09c", "guid": "bfdfe7dc352907fc980b868725387e989d4ce3f32ced980fd38cf04e2f74bd06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836da1b95ee19a2388c62618c47abe53c", "guid": "bfdfe7dc352907fc980b868725387e981de9335707d5a34889b3c432fa94aa1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2125ece11c8f5b0dc53451a5ae454bf", "guid": "bfdfe7dc352907fc980b868725387e984b8b20ac706c8c16318c9d395f2a952f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd44bb2bd47ef1b763c7f77f978284d4", "guid": "bfdfe7dc352907fc980b868725387e981b5d654370b16b8d3dc565e61759c438"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98457b38747647fa2d3d99e0757a4e9b0a", "guid": "bfdfe7dc352907fc980b868725387e985452e51a626baf7585a0091fbf9df58d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876735c6247cb2f4f49d8a167fec14d22", "guid": "bfdfe7dc352907fc980b868725387e9819e8601b1062f3c77ee5e191a812d567"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5e81394e8d4918e92c243946d3e874", "guid": "bfdfe7dc352907fc980b868725387e98041e5f5605883a1add1d62533147d579"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982106fe8be9807fec098bab8c1501908d", "guid": "bfdfe7dc352907fc980b868725387e98e8ab8e9440fabc084574c1ee268b28f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac267ed5ba8d823be38c7d574e56bc5", "guid": "bfdfe7dc352907fc980b868725387e98bbcf3b3e645227da89fa07e75bb67a2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ec6969c04df020077aaeac3f0b9667", "guid": "bfdfe7dc352907fc980b868725387e98ebd0a03d0e7c3e72358ef35ce1568d59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be08df0e0e21099f187a46c29d5abca", "guid": "bfdfe7dc352907fc980b868725387e98ee555bd343e6022ea13fd48aed8d0be3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831757b62d562dd4ad2d2f9f705216a43", "guid": "bfdfe7dc352907fc980b868725387e98b0e43cfd3e35e4374326dfe51117ee6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3c2cf64cdf587dda98459b2b98b683a", "guid": "bfdfe7dc352907fc980b868725387e98230d0a81edee27bf0d9efe2cf88124ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ad62583810258cca9ce7d518a9ffac", "guid": "bfdfe7dc352907fc980b868725387e98968bc56a0cf50c4b5b05d73d6ea07a28"}], "guid": "bfdfe7dc352907fc980b868725387e98f056575a38519a339ce5b6e7812b51cc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e9804c72a212ef4ade57b15357711c385a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7dd3308e62ff3506ddc7ba6752920a", "guid": "bfdfe7dc352907fc980b868725387e98c1e119995379487ac8bd941d34e31830"}], "guid": "bfdfe7dc352907fc980b868725387e982cd75e34a72b7653e036761967c6c93d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9850eb1ee49b25ca59fddf5ff83f190914", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9853f0dc66645b9a7f1583ab7f233220fa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}