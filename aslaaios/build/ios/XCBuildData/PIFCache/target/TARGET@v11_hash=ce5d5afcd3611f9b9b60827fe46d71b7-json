{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9843505e38067aaf48521b7d9da9914eed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982dec964c2278048016a033c29e7a316d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e101aa7bf8f26241b71e635c96737324", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845c4d4f479579a177c8f56d77eb01c60", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e101aa7bf8f26241b71e635c96737324", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_MICROPHONE=1 PERMISSION_PHOTOS=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_NO_PIE": "NO", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ab0c970ab8d4da0c8b38e92ddb4df58", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e408d5380963087298bbdd1541b5263d", "guid": "bfdfe7dc352907fc980b868725387e987d6460a3982f109b33e40228409e356b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964b9a9349b6f840c2e2d955ed9ddf20", "guid": "bfdfe7dc352907fc980b868725387e98c1f10422709f5f239ab92b24e0b05177", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca1ae6e27892c767a547cb18992d487", "guid": "bfdfe7dc352907fc980b868725387e9856eff768d09107dc3d4b4fb23ddebeff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ae9abd2ae435359a93b2b6a58efddce", "guid": "bfdfe7dc352907fc980b868725387e98f5a62999fad9a3fa9e356393573931c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983425ff44f64306968fd67e1ae4913f80", "guid": "bfdfe7dc352907fc980b868725387e9876f59625ee4d9301bacf22aefb945fc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd16946d939e3f8839873ad1d025dede", "guid": "bfdfe7dc352907fc980b868725387e98847d180c1ef997d81c197cc308d94a05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee1e25d79bea7142f0484e44c85a3cb", "guid": "bfdfe7dc352907fc980b868725387e98639e6004ebaf90e12f84017011a2a247", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddd05cb5db5c15844e3feab0c2b07bc3", "guid": "bfdfe7dc352907fc980b868725387e98bc10cac03912da4969ca2401f9c523b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c0c7cb4e6ae0933c13fd82a8098304", "guid": "bfdfe7dc352907fc980b868725387e98cb96db9305e25fed5c3071a746bed8d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fcb30e24ffeb1b97d8f61504fb912cd", "guid": "bfdfe7dc352907fc980b868725387e98eabe3c5c1a606186c9d39df2e741dc72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db055114466616e84b8006df70c8054f", "guid": "bfdfe7dc352907fc980b868725387e9814a5343f48b0273d702d9e890a8edf45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d525cda38fe8ee14cde28f428ce42030", "guid": "bfdfe7dc352907fc980b868725387e98be582e01e5ae2356dd125d5715c172bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0e1e01947b8da9631914236f82a9ee0", "guid": "bfdfe7dc352907fc980b868725387e98f8d8e517a025d695e174212c905434e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c9ebe641006b08857009bc9594cb02a", "guid": "bfdfe7dc352907fc980b868725387e98c190fa9dc53499c23159a0d321d96da7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982df9dabd3c1656069fd46b2cfc849c6c", "guid": "bfdfe7dc352907fc980b868725387e988288c6fb6a227e58184b8f363d12bc5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e2d79f9569db35706bda4c1192f0cf", "guid": "bfdfe7dc352907fc980b868725387e9897d4742694ff395ca05e8041feb9267d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872139ddf22e8dc3bae97db989eb6ff56", "guid": "bfdfe7dc352907fc980b868725387e9824babb297da7d7d23cc2c841d010cc35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98447442989d98ffb77defb78be6af41e8", "guid": "bfdfe7dc352907fc980b868725387e9853fe63024e3ac38bd6dd181c09da0740", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6c45daabd3e233e4dce3fdcce0f8ec", "guid": "bfdfe7dc352907fc980b868725387e9859b92022e6dfe798d47ca96cd498dfe9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986841f41804df28c594806a2a8de79e2e", "guid": "bfdfe7dc352907fc980b868725387e98754b90c0390d558cef564a21131e40fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce6ed583b279aea7c2f4453466c34c39", "guid": "bfdfe7dc352907fc980b868725387e98179483de5bf29eb2d180e372358a348e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980248ea7af379aa71d8252de19dad6e20", "guid": "bfdfe7dc352907fc980b868725387e98dea3ef8a993c846658098dba1d3c1a22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8fe6dd5cdf433af63d564309885531e", "guid": "bfdfe7dc352907fc980b868725387e98f4f798b3b74c728e5df0acd1df8d291d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c159604a0bfca5a2f1e8267862d132c", "guid": "bfdfe7dc352907fc980b868725387e988dac2836170d139444d9a298ce79cec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ed88c968db59b2e2cc7528d8e67a9a", "guid": "bfdfe7dc352907fc980b868725387e981db106853033cb922be73db87a74d4e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c085712c3a4fea32ea219c1129c01cf", "guid": "bfdfe7dc352907fc980b868725387e9866495606b28e7696f1034137163d50a6"}], "guid": "bfdfe7dc352907fc980b868725387e9857e2d461e06070eabd1da64554e3fe11", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a28cc621b3f853d31fa27f6ea62aefe8", "guid": "bfdfe7dc352907fc980b868725387e981cf023e841f56bfa63da0943ceffd3c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890ed725b8bcdafd6c26f7134a2518916", "guid": "bfdfe7dc352907fc980b868725387e98893f73a4f4e9a48bb6713c0a8e4264c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f5836c6f486d7618e35c509b1814c62", "guid": "bfdfe7dc352907fc980b868725387e98235e1ba0a80c30bbae8917fb88c4dfa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982186516ee4ce5e25cfad687904116ee1", "guid": "bfdfe7dc352907fc980b868725387e9839cca41a284eb56a986706b23b53af9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828302f7903748235ddcb2d6d68a29a20", "guid": "bfdfe7dc352907fc980b868725387e983597ee699e462d59984672fc61d9c76a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98231216202677714fc3617fe62c815341", "guid": "bfdfe7dc352907fc980b868725387e982c4755c78d25ddc966f0e2f3a056dcf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02e6978da4a96e4aed6e64d874317d2", "guid": "bfdfe7dc352907fc980b868725387e98434ae665d93f5f67f239b29334c7de68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d774d0aa89c0a0049d608ad0729a962", "guid": "bfdfe7dc352907fc980b868725387e983c2aafd97fcb79c363d8eeef38af2460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb95ee7463cf81d6c1b5e288df477944", "guid": "bfdfe7dc352907fc980b868725387e98c9151c77cfa9c36772ee4719b4a0693f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a640784ab4add3d93fab368e44dbeff9", "guid": "bfdfe7dc352907fc980b868725387e98eef3e3e776d85d2f959a949000f306ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fde6f00206c7ea695d5fcd65f03a76b6", "guid": "bfdfe7dc352907fc980b868725387e98e558f453053a91755790acf78de4e4ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98110b495aa3c0518530ff66cf1cd9b614", "guid": "bfdfe7dc352907fc980b868725387e983068452c118bf0d6ac83cb726f0f9195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af1241e35b8098dc0ed7de761eb966c", "guid": "bfdfe7dc352907fc980b868725387e98c39fa9cd3614b3b7a45199e0ceced251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706143f5ffea8d93ef402daa0c7cad4c", "guid": "bfdfe7dc352907fc980b868725387e98bfe0d370b8432eabf63644f18a40dac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e89ace186b6e4ae837a2d6be0bc71b3e", "guid": "bfdfe7dc352907fc980b868725387e983bf30e1e6de3867c500ce449006e72cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898673d6c8a776de9d3be83a9c947b631", "guid": "bfdfe7dc352907fc980b868725387e98dff9cd1222965ceb83e79ffbcc6b2548"}], "guid": "bfdfe7dc352907fc980b868725387e985fbab1ec9810402045e8a80ccbc5eb6a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983237a26cbe70be1ccd8aaf134c512504", "guid": "bfdfe7dc352907fc980b868725387e98a74e03384148af4ff6ccc19aa99bdf17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870350cda99093f910f2f9eecd3826fd9", "guid": "bfdfe7dc352907fc980b868725387e98e6375ef00b8bb0d80cb5b3206db581ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986610c408657e64db4bdab3f032e5a171", "guid": "bfdfe7dc352907fc980b868725387e987e93229c709c1f23c07ab8bdad390c06"}], "guid": "bfdfe7dc352907fc980b868725387e985e70c8b28987ff06b277cc05c9487cdf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980fd152ae92dae93b2ea1bf132df07932", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e983f03ad20fe661f69e277ad5ea80a84a7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}