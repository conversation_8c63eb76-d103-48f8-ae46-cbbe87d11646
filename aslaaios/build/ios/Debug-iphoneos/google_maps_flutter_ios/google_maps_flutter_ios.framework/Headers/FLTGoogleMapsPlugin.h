// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#import <Flutter/Flutter.h>
#import <GoogleMaps/GoogleMaps.h>

#import "FGMClusterManagersController.h"
#import "GoogleMapCircleController.h"
#import "GoogleMapController.h"
#import "GoogleMapMarkerController.h"
#import "GoogleMapPolygonController.h"
#import "GoogleMapPolylineController.h"

NS_ASSUME_NONNULL_BEGIN

@interface FLTGoogleMapsPlugin : NSObject <FlutterPlugin>
@end

NS_ASSUME_NONNULL_END
