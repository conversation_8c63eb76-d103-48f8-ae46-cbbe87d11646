/* Copyright (c) 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import <Foundation/Foundation.h>

#import <GoogleMaps/GoogleMaps.h>

#import "GMUGeometryContainer.h"
#import "GMUStyle.h"
#import "GMUStyleMap.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * Instances of this class render geometries generated by a GMUKMLParser or
 * GMUGeoJSONParser object. These geometries can have specified style information
 * applied to them when being rendered.
 */
@interface GMUGeometryRenderer : NSObject

/**
 * Initializes a new renderer.
 *
 * @param map the Google Map layer to render the geometries onto.
 * @param geometries the geometries to be rendered.
 */
- (instancetype)initWithMap:(GMSMapView *)map
                 geometries:(NSArray<id<GMUGeometryContainer>> *)geometries;
/**
 * Initializes a new renderer.
 *
 * @param map the Google Map layer to render the geometries onto.
 * @param geometries the geometries to be rendered.
 * @param styles the styles to be applied to the geometries.
 */
- (instancetype)initWithMap:(GMSMapView *)map
                 geometries:(NSArray<id<GMUGeometryContainer>> *)geometries
                     styles:(NSArray<GMUStyle *> *_Nullable)styles;
/**
 * Initializes a new renderer.
 *
 * @param map the Google Map layer to render the geometries onto.
 * @param geometries the geometries to be rendered.
 * @param styles the styles to be applied to the geometries.
 * @param styleMaps the styleMaps to be applied to the geometries
 */
- (instancetype)initWithMap:(GMSMapView *)map
                 geometries:(NSArray<id<GMUGeometryContainer>> *)geometries
                     styles:(NSArray<GMUStyle *> * _Nullable)styles
                  styleMaps:(NSArray<GMUStyleMap *> *_Nullable)styleMaps;
/**
 * Renders the geometries onto the Google Map.
 */
- (void)render;

/**
 * Removes the rendered geometries from the Google Map. Markup that was not added by the renderer is
 * preserved.
 */
- (void)clear;

@end

NS_ASSUME_NONNULL_END
