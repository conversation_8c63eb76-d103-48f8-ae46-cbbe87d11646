<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>GoogleToolboxForMac_Logger_Privacy.bundle/Info.plist</key>
		<data>
		encd5Sk9H0fPTuLowln81HWiTV0=
		</data>
		<key>GoogleToolboxForMac_Logger_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		GqeAMkwbcNQeG0K4qQhQh2vHhHo=
		</data>
		<key>GoogleToolboxForMac_Privacy.bundle/Info.plist</key>
		<data>
		UR7bp33atp4SQZ10hLW+6TH6cS8=
		</data>
		<key>GoogleToolboxForMac_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZajnvEs/MYRS3X4TPLAhBWi8mc4=
		</data>
		<key>Info.plist</key>
		<data>
		rJzYtcqVe8tzoGwjIazdc+7UDwM=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>GoogleToolboxForMac_Logger_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			OTfOxchdNumSH5TNT2NfSni4X6/9Q8AsfSd2GxIRlBs=
			</data>
		</dict>
		<key>GoogleToolboxForMac_Logger_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			PkqTy+hqzvfdfgY6KMhJmS9Vbn9SytxfN8HosOG1RoY=
			</data>
		</dict>
		<key>GoogleToolboxForMac_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tXxsQUXJDxhfaYmqr2EM/I7PfyeKEpBqkZHIWxfccxY=
			</data>
		</dict>
		<key>GoogleToolboxForMac_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			dLDNcvwjwe8wLyLuJ1P2GBfNxa8P96fy0GMrUk+4rOo=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
