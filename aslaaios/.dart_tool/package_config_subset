_discoveryapis_commons
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_discoveryapis_commons-1.0.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_discoveryapis_commons-1.0.7/lib/
_flutterfire_internals
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.58/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.58/lib/
animated_rotation
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/animated_rotation-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/animated_rotation-2.0.0/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/
audioplayers
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-6.5.0/lib/
audioplayers_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-5.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-5.2.1/lib/
audioplayers_darwin
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-6.3.0/lib/
audioplayers_linux
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-4.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-4.2.1/lib/
audioplayers_platform_interface
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/
audioplayers_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-5.1.1/lib/
audioplayers_windows
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-4.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-4.2.1/lib/
auto_size_text
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/lib/
awesome_snackbar_content
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/awesome_snackbar_content-0.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/awesome_snackbar_content-0.1.2/lib/
badges
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/badges-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/badges-3.1.2/lib/
base32
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/base32-2.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/base32-2.1.3/lib/
bluez
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bluez-0.8.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bluez-0.8.3/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
cached_network_image
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.2.1/lib/
cached_network_image_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-1.0.0/lib/
cached_network_image_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.0.1/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
circular_countdown_timer
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/circular_countdown_timer-0.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/circular_countdown_timer-0.2.4/lib/
cli_util
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.3.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.3.5/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
connectivity_plus
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib/
connectivity_plus_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
custom_date_range_picker
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_date_range_picker-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_date_range_picker-1.1.0/lib/
custom_marker
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_marker-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/custom_marker-1.0.0/lib/
data_table_2
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/data_table_2-2.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/data_table_2-2.6.0/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib/
emoji_flag_converter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/emoji_flag_converter-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/emoji_flag_converter-1.1.0/lib/
event_bus
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/event_bus-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/event_bus-2.0.1/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_core
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/lib/
firebase_core_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/lib/
firebase_core_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/lib/
firebase_messaging
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/lib/
firebase_messaging_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.9/lib/
firebase_messaging_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.9/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
flutter_animarker
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animarker-3.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animarker-3.2.0/lib/
flutter_animate
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-1.0.0/lib/
flutter_blue_plus
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.35.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.35.5/lib/
flutter_blue_plus_android
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_android-4.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_android-4.0.5/lib/
flutter_blue_plus_darwin
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_darwin-4.0.1/lib/
flutter_blue_plus_linux
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_linux-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_linux-3.0.2/lib/
flutter_blue_plus_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_platform_interface-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_platform_interface-4.0.2/lib/
flutter_blue_plus_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_web-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_web-3.0.1/lib/
flutter_blurhash
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blurhash-0.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blurhash-0.7.0/lib/
flutter_cache_manager
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/
flutter_launcher_icons
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.10.0/lib/
flutter_local_notifications
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/lib/
flutter_local_notifications_linux
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/lib/
flutter_local_notifications_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.2.0/lib/
flutter_page_lifecycle
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_page_lifecycle-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_page_lifecycle-1.1.0/lib/
flutter_plugin_android_lifecycle
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib/
flutter_reactive_ble
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_reactive_ble-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_reactive_ble-5.4.0/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/lib/
flutter_sound
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.16.3/lib/
flutter_sound_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_platform_interface-9.16.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_platform_interface-9.16.3/lib/
flutter_sound_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.16.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.16.3/lib/
flutter_svg
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-1.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-1.1.6/lib/
font_awesome_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.1.0/lib/
from_css_color
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/from_css_color-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/from_css_color-2.0.0/lib/
functional_data
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functional_data-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functional_data-1.2.0/lib/
geolocator
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-9.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-9.0.2/lib/
geolocator_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/lib/
geolocator_apple
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/lib/
geolocator_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/lib/
geolocator_web
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/lib/
geolocator_windows
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.1.3/lib/
go_router
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-12.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-12.1.3/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-4.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-4.0.5/lib/
google_identity_services_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_maps
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps-8.1.1/lib/
google_maps_flutter
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter-2.12.3/lib/
google_maps_flutter_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.16.2/lib/
google_maps_flutter_ios
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.15.4/lib/
google_maps_flutter_platform_interface
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.12.1/lib/
google_maps_flutter_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.12+2/lib/
googleapis
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/googleapis-13.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/googleapis-13.2.0/lib/
googleapis_auth
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/googleapis_auth-1.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/googleapis_auth-1.6.0/lib/
html
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/lib/
http
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-3.3.0/lib/
image_picker
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-0.8.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-0.8.9/lib/
image_picker_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/
image_picker_for_web
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-2.2.0/lib/
image_picker_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/
image_picker_linux
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/
image_picker_macos
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/
image_picker_platform_interface
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/
js
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.7.2/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
json_path
2.16
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_path-0.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_path-0.4.1/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
logger
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/
mobile_scanner
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/lib/
mqtt_client
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mqtt_client-10.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mqtt_client-10.8.0/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
nm
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/lib/
octo_image
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-1.0.2/lib/
osrm
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/osrm-0.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/osrm-0.0.8/lib/
otp
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/otp-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/otp-3.1.4/lib/
page_transition
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/page_transition-2.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/page_transition-2.0.4/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_drawing
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_drawing-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_drawing-1.0.1/lib/
path_parsing
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib/
path_provider
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
permission_handler
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/lib/
permission_handler_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/lib/
permission_handler_apple
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib/
permission_handler_html
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib/
permission_handler_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/
permission_handler_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib/
petitparser
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-5.4.0/lib/
pin_code_fields
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
protobuf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-2.1.0/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/
qr
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/lib/
qr_flutter
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_flutter-4.1.0/lib/
reactive_ble_mobile
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/reactive_ble_mobile-5.4.0/lib/
reactive_ble_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/reactive_ble_platform_interface-5.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/reactive_ble_platform_interface-5.4.0/lib/
recase
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/lib/
rfc_6901
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rfc_6901-0.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rfc_6901-0.1.1/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/
sanitize_html
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sanitize_html-2.1.0/lib/
shared_preferences
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2/lib/
shared_preferences_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
socket_io_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_client-3.1.2/lib/
socket_io_common
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/socket_io_common-3.1.1/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
sqflite
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.0/lib/
sqflite_android
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/
sqflite_common
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/
sqflite_darwin
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/
sqflite_platform_interface
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stop_watch_timer
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stop_watch_timer-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stop_watch_timer-3.2.1/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
synchronized
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.1/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
timeago
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/
timezone
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.4/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/
url_launcher_android
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/
url_launcher_ios
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib/
url_launcher_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
visibility_detector
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/visibility_detector-0.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/visibility_detector-0.3.3/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.3.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
aslaa
3.0
file:///Users/<USER>/somecode/aslaa/web/aslaaios/
file:///Users/<USER>/somecode/aslaa/web/aslaaios/lib/
sky_engine
3.7
file:///Users/<USER>/somecode/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/somecode/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/somecode/flutter/packages/flutter/
file:///Users/<USER>/somecode/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/somecode/flutter/packages/flutter_localizations/
file:///Users/<USER>/somecode/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///Users/<USER>/somecode/flutter/packages/flutter_test/
file:///Users/<USER>/somecode/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/somecode/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/somecode/flutter/packages/flutter_web_plugins/lib/
2
